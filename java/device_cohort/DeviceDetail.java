package device_cohort;

import java.io.BufferedReader;

import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.Map.Entry;

import org.apache.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;

import pojo.DeviceCohort;
import pojo.PeriodDetail;

public class DeviceDetail {
	static final Logger log = Logger.getLogger(DeviceDetail.class);
	public ArrayList<PeriodDetail> getRegisteredList(Connection db_connection_iris, String startMonthDate, String endMonthDate,int monthNo) {
		log.info("Entered getRegisteredList");
		ArrayList<PeriodDetail>periodDetailList= new ArrayList<PeriodDetail>();

		try {
			String selectRegisteredQry="SELECT COUNT(DS.user_id) AS count_userid,DATE_FORMAT(DS.instal_date,'%b')AS mn,S.sales_channel,DS.first_period "
					+ "FROM saas_order_detail S JOIN device_subscription DS ON S.user_id=DS.user_id WHERE  S.category='registered'" + 
					"AND  S.order_date BETWEEN ? AND ? GROUP BY mn,S.sales_channel,DS.first_period ORDER BY mn ;";

			log.info("RegisteredQuery: "+selectRegisteredQry);
			PreparedStatement pstmt = null;
			pstmt = db_connection_iris.prepareStatement(selectRegisteredQry);
			pstmt.setString(1, startMonthDate);
			pstmt.setString(2, endMonthDate);
			ResultSet rs = pstmt.executeQuery();
			while(rs.next()) {
				PeriodDetail periodDetail= new PeriodDetail();
				String saleschannel=rs.getString("sales_channel").toLowerCase();
				String firstperiod=rs.getString("first_period").toLowerCase();
				int count=Integer.parseInt(rs.getString("count_userid"));
				String monthregistered=rs.getString("mn");

				if(saleschannel.equalsIgnoreCase("NA")) {
					saleschannel="others";
				}

				if(firstperiod.equalsIgnoreCase("NA")) {
					firstperiod="others";
				}



				periodDetail.setSaleschannel(saleschannel);
				periodDetail.setFirst_period(firstperiod);
				periodDetail.setCount(count);
				periodDetail.setMonth_no(monthNo);
				periodDetail.setMonthname(monthregistered);
				periodDetailList.add(periodDetail);
			}

		}catch (Exception e) {
			log.error("Error in getRegisteredList"+e.getLocalizedMessage());
		}
		return periodDetailList;
	}

	private String getMonthName(String date) {
		int monthno=0;
		String mname="NA";
		log.info("Entered into getMonthName");
		try {
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
			Date formattedDate = dateFormat.parse(date);
			monthno=Integer.parseInt(new SimpleDateFormat("MM").format(formattedDate));
			String[] monthNames = {"January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"};
			mname=monthNames[monthno-1];

		}catch (Exception e) {
			log.error("Exception in getMonthName : " + e.getLocalizedMessage());
		}


		return mname;
	}

	public ArrayList<PeriodDetail> getUnidentifiedRegisteredList(Connection db_connection_iris, String startMonthDate, String endMonthDate, int monthno) {
		log.info("Entered getUnidentifiedRegisteredList");
		ArrayList<PeriodDetail>periodDetailList= new ArrayList<PeriodDetail>();
		PeriodDetail periodDetail= new PeriodDetail();
		try {
			String selectRegisteredQry="SELECT COUNT(DS.user_id) AS count_userid,DATE_FORMAT(DS.instal_date,'%b')AS mn,S.sales_channel,DS.first_period \r\n" + 
					"FROM saas_order_detail S JOIN device_subscription DS ON S.user_id=DS.user_id WHERE  S.category='unIdentifiedregistered'\r\n" + 
					"AND  S.order_date BETWEEN ? AND ? GROUP BY mn,S.sales_channel,DS.first_period ORDER BY mn ;";

			log.info("RegisteredQuery: "+selectRegisteredQry);
			PreparedStatement pstmt = null;
			pstmt = db_connection_iris.prepareStatement(selectRegisteredQry);
			pstmt.setString(1, startMonthDate);
			pstmt.setString(2, endMonthDate);
			ResultSet rs = pstmt.executeQuery();
			while(rs.next()) {
				String saleschannel=rs.getString("sales_channel");
				String firstperiod=rs.getString("first_period");
				int count=Integer.parseInt(rs.getString("count_userid"));
				String monthregistered=rs.getString("mn");

				if(saleschannel.equalsIgnoreCase("NA")) {
					saleschannel="others";
				}

				if(firstperiod.equalsIgnoreCase("NA")) {
					firstperiod="others";
				}

				periodDetail.setSaleschannel(saleschannel);
				periodDetail.setFirst_period(firstperiod);
				periodDetail.setCount(count);
				periodDetail.setMonth_no(monthno);
				periodDetail.setMonthname(monthregistered);
				periodDetailList.add(periodDetail);

			}

		}catch (Exception e) {
			log.error("Error in getUnidentifiedRegisteredList"+e.getLocalizedMessage());
		}
		return periodDetailList;
	}

	public ArrayList<PeriodDetail> getUnidentifiedActivatedList(Connection db_connection_iris, String startMonthDate, String endMonthDate,int monthno) {
		log.info("Entered getUnidentifiedRegisteredList");
		ArrayList<PeriodDetail>periodDetailList= new ArrayList<PeriodDetail>();
		PeriodDetail periodDetail= new PeriodDetail();
		try {
			String selectRegisteredQry="SELECT COUNT(DS.user_id) AS count_userid,DATE_FORMAT(DS.instal_date,'%b')AS mn,S.sales_channel,DS.first_period \r\n" + 
					"FROM saas_order_detail S JOIN device_subscription DS ON S.user_id=DS.user_id WHERE  S.category='unIdentifiedactivated'\r\n" + 
					"AND  S.order_date BETWEEN ? AND ? GROUP BY mn,S.sales_channel,DS.first_period ORDER BY mn ;";

			log.info("RegisteredQuery: "+selectRegisteredQry);
			PreparedStatement pstmt = null;
			pstmt = db_connection_iris.prepareStatement(selectRegisteredQry);
			pstmt.setString(1, startMonthDate);
			pstmt.setString(2, endMonthDate);
			ResultSet rs = pstmt.executeQuery();
			while(rs.next()) {
				String saleschannel=rs.getString("sales_channel");
				String firstperiod=rs.getString("first_period");
				int count=Integer.parseInt(rs.getString("count_userid"));
				String monthregistered=rs.getString("mn");

				if(saleschannel.equalsIgnoreCase("NA")) {
					saleschannel="others";
				}

				if(firstperiod.equalsIgnoreCase("NA")) {
					firstperiod="others";
				}

				periodDetail.setSaleschannel(saleschannel);
				periodDetail.setFirst_period(firstperiod);
				periodDetail.setCount(count);
				periodDetail.setMonth_no(monthno);
				periodDetail.setMonthname(monthregistered);
				periodDetailList.add(periodDetail);
			}

		}catch (Exception e) {
			log.error("Error in getUnidentifiedRegisteredList"+e.getLocalizedMessage());
		}
		return periodDetailList;
	}

	public DeviceCohort getMonthCount(ArrayList<PeriodDetail> deviceCohortList) {
		log.info("Entered getMonthCount");
		try {
			String saleschannel="NA";
			Set<String>saleschannelList=new HashSet<>();
			for(PeriodDetail det:deviceCohortList) {
				saleschannel=det.getSaleschannel();

				saleschannelList.add(saleschannel);
			}

		}catch (Exception e) {
			log.error("Error in getMonthCount"+e.getLocalizedMessage());
		}
		return null;
	}

	public Map<String, Integer> getDeviceCohortList(Connection db_connection_iris, String startMonth,
			String endMonth, int monthno) {
		log.info("Entered getDeviceCohortList");
		Map<String,Integer> dcList= new HashMap<String, Integer>();
		ArrayList<PeriodDetail> periodDetailList= new ArrayList<PeriodDetail>();

		Set<String>salesch=new HashSet<>();
		Set<String>perioddet=new HashSet<>();
		Set<String>categoryDetail=new HashSet<>();
		Map<String,Integer>totalSale= new HashMap<String,Integer>();

		
		int cnt=0;
		int same_month_active_count=0;

		try {

			String select_qry="SELECT DATE_FORMAT(instal_date,'%b') AS install_month,SO.sales_channel,category,lower(first_period) as first_period,COUNT(SO.id) AS cnt FROM `saas_order_detail` SO " + 
					"LEFT JOIN device_subscription DS ON SO.user_id=DS.user_id WHERE SO.order_date BETWEEN ?  AND ? " + 
					"GROUP BY MONTH(instal_date),SO.sales_channel,category order by sales_channel;";

			log.info("SelectQuery: "+select_qry);
			PreparedStatement pstmt = null;
			pstmt = db_connection_iris.prepareStatement(select_qry);
			pstmt.setString(1, startMonth);
			pstmt.setString(2, endMonth);
			ResultSet rs = pstmt.executeQuery();
			while(rs.next()) {
				PeriodDetail pdetail = new PeriodDetail();
				String monthName=rs.getString("install_month");
				String saleschannel=rs.getString("sales_channel");
				int count=Integer.parseInt(rs.getString("cnt"));	
				String category =rs.getString("category");
				String period=rs.getString("first_period");
				if(saleschannel.equalsIgnoreCase("NA")) {
					saleschannel="others";
				}		

				if(monthName==null) {
					continue;

				}
				if(period==null) {
					period="other";

				}

				pdetail.setMonthname(monthName);
				pdetail.setSaleschannel(saleschannel);
				pdetail.setCount(count);
				pdetail.setCategory(category);
				pdetail.setFirst_period(period);
				periodDetailList.add(pdetail);	
			}
			System.out.println();
			for(PeriodDetail detail:periodDetailList) {
				String orderchannel=detail.getSaleschannel().toLowerCase();
				String period=detail.getFirst_period().toLowerCase();
				int count=detail.getCount();
				String category=detail.getCategory().toLowerCase();
				monthno=detail.getMonth_no();
				String month=detail.getMonthname().toLowerCase();
				String key=orderchannel+":"+period+":"+category+":"+month;
				categoryDetail.add(category);
				salesch.add(orderchannel);
				perioddet.add(period);
				totalSale.put(key, count);
			}

			String month[]= {"jan","feb","mar","apr","may","jun","jul","aug","sep","oct","nov","dec",};
			System.out.println();
			String searchKey="";
			String key="";
			for (String orderchannel : salesch) {

				for(String period : perioddet) {

					for(String category : categoryDetail) {

						for(String mn : month) {
							key=orderchannel+":"+period+":"+category+":"+mn;
							log.info("Search key : "+key);
							searchKey=key;
							if(totalSale.containsKey(searchKey)) {
								cnt=totalSale.get(searchKey);
								same_month_active_count=cnt;
								dcList.put(searchKey,cnt );
							}
						}
					}
				}
			}


		}catch(Exception e) {
			log.error("Error in getDeviceCohortList"+e.getLocalizedMessage());
		}
		return dcList;
	}

	public Map<String,String> getAppDwldDetails( String amplitude_url, String start, String end, String username, String pass, List<String> month_days) {
		log.info("Entered getAppDwldDetails:");
		Map<String,String>appdwldCount= new HashMap<String, String>();
		try {
			String startDate=getDateFormat(start);
			String[] splitted_stDate=startDate.split("#");
			String endDate=getDateFormat(end);
			String[] splitted_enddate=endDate.split("#");
			String response=getHttpResponse(amplitude_url,splitted_stDate[0],splitted_enddate[0],username,pass);
			JSONObject jsonObj = new JSONObject(response);
			JSONObject dataObject = jsonObj.getJSONObject("data");
			JSONArray dateArray=dataObject.getJSONArray("xValues");
			System.out.println(dateArray);

			JSONArray dwldCounts=dataObject.getJSONArray("series");
			JSONArray iosCounts = (JSONArray) dwldCounts.get(0);
			JSONArray androidCounts = (JSONArray) dwldCounts.get(1);
			for (int i = 0; i < dateArray.length(); i++) {
				for(int j=0;j<iosCounts.length();j++) {
					String date = dateArray.get(i).toString();
					int ioscnt=(int) iosCounts.get(i);
					int androidcnt=(int)androidCounts.get(i);
					appdwldCount.put(date, ioscnt+","+androidcnt);
					break;
				}

			}					
		}catch (Exception e) {
			log.error("Error in getAppDwldDetails:"+e.getLocalizedMessage());
		}
		return appdwldCount;
	}

	private String getDateFormat(String date) {
		log.info("Entered getDateFormat");
		String formatteddate="NA";
		try {
			Date processDate=new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").parse(date); 
			Calendar cal = Calendar.getInstance();
			cal.setTime(processDate);
			String month = String.valueOf((cal.get(Calendar.MONTH))+1);
			String year =String.valueOf( cal.get(Calendar.YEAR));
			String day = String.valueOf(cal.get(Calendar.DATE));
			System.out.println(month+year+day);
			if(month.length()!=2) {
				month="0"+month;
			}

			if(day.length()!=2) {
				day="0"+day;
			}
			formatteddate=year+month+day+"#"+month;
		}catch (Exception e) {
			log.error("Error in getDateFormat:"+e.getLocalizedMessage());
		}
		return formatteddate;
	}

	public static String getHttpResponse(String url, String startdate, String enddate,String username,String password) {
		log.info("Getting the Http Response Object...!");
		try {
			String urlParams = "start="+startdate+"&end="+enddate+"&m=new&g=platform";
			url=url+urlParams;
			URL _webServiceURL = new URL(url);
			HttpURLConnection conn = (HttpURLConnection) _webServiceURL.openConnection();

			conn.setRequestMethod("GET");

			StringBuffer response = new StringBuffer();
			String inline = "";

			String userpass = username+":"+password;

			String basicAuth = "Basic " + new String(Base64.getEncoder().encode(userpass.getBytes()));

			conn.setRequestProperty("Authorization", basicAuth);

			BufferedReader in = null;
			int responseCode = conn.getResponseCode();
			if (responseCode >= 400) {

				in = new BufferedReader(new InputStreamReader(conn.getErrorStream(), "UTF-8"));
				while ((inline = in.readLine()) != null) {
					response.append(inline);
					response.append("\n");
				}

				in.close();
			} else {
				in = new BufferedReader(new InputStreamReader(conn.getInputStream()));

				while ((inline = in.readLine()) != null) {
					response.append(inline);
				}
				in.close();
			}

			log.info("Returning the Http Response Object...!");
			return response.toString();
		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
		}
		return null;
	}


	public Map<String,Integer> getOrdersTotalSaleList(Connection db_connection_niom, String start,
			String end) {
		Map<String, Integer>totalDeviceCohortList= new HashMap<String, Integer>();
		log.info("Entered getOrdersTotalSaleList ");
		String saleschannel="NA";
		try {

			String select_query="SELECT DATE_FORMAT(`datetime`,'%b') AS order_month,COUNT(order_id) AS cnt, OAT.acc_type AS sales_channel FROM niom.orders  JOIN `order_account`" + 
					"OAT ON order_acc_typeid = OAT.id WHERE `datetime` BETWEEN ? AND ? GROUP BY sales_channel;";

			PreparedStatement pstmt = null;
			pstmt = db_connection_niom.prepareStatement(select_query);
			System.out.println(select_query);
			pstmt.setString(1, start);
			pstmt.setString(2, end);
			ResultSet rs = pstmt.executeQuery();
			while (rs.next()) {
				String salesch=rs.getString("sales_channel");
				String month=rs.getString("order_month");
				int count=Integer.parseInt(rs.getString("cnt"));
				totalDeviceCohortList.put(month+"-"+salesch, count);


			}
		}catch (Exception e) {
			log.error("Error in getOrdersTotalSaleList"+e.getLocalizedMessage());
		}finally {
			try {
				db_connection_niom.close();
			} catch (SQLException e) {
				log.error("Error in getOrdersTotalSaleList: Connection error "+e.getLocalizedMessage());
			}
		}
		return totalDeviceCohortList;
	}
	
	
//	public void saveIntoDeviceCohort(int year,Map<Integer, ArrayList<DeviceCohort>> totalActivatedMapList, Properties prop, Connection db_connection_iris) {
//		log.info("Entered saveIntoDeviceCohort!");
//		
//		for( Entry<Integer, ArrayList<DeviceCohort>> dcList : totalActivatedMapList.entrySet()) {
//
//			ArrayList<DeviceCohort>list=dcList.getValue();
//			for(DeviceCohort dc:list) {
//				try {
//				String insertQuery = "INSERT INTO `device_cohort` (`year`, `month`,sales_channel, `total_sales`, `same_month_active`, "
//						+ " `jan`,`feb`,`mar`,`apr`,`may`,`jun`,`jul`,`aug`,`sep`,`oct`,`nov`,`dec`,month_no,period,un_identified_registered,un_identified_activated) VALUES ("
//						+ year+",'"+dc.getMonth()+"','"+dc.getSales_ch()+"',"+dc.getTotal_sales()+","+dc.getSame_month_active()+","
//						 + dc.getJanTotCnt() + "," + dc.getFebTotCnt() + "," + dc.getMarTotCnt() 
//						+ "," + dc.getAprTotCnt()+ "," + dc.getMayTotCnt()+ "," + dc.getJunTotCnt() + ","+ dc.getJulTotCnt()+","+dc.getAugTotCnt()+","+dc.getSepTotCnt()
//						+","+dc.getOctTotCnt()+","+dc.getNovTotCnt()+","+dc.getDecTotCnt()+","+dc.getMonth_no()+",'"+dc.getPeriod()+"',"+dc.getUnindentifiedregisteredcount()+","+dc.getUnidentifiedactivatecount());";
//				
//			log.info("Insert Query - " + insertQuery);
//			
//			int inputStatus = db_connection_iris.prepareStatement(insertQuery).executeUpdate();
//			
//			if (inputStatus > 0)
//				log.info("Row inserted for yr: " + year+" : "+dc.getMonth_no());
//			else
//				log.info("Row not inserted for yr: " + year+" : "+dc.getMonth_no());
//			
//			} catch (Exception e) {
//				
//			try {
//				String updateQuery = "UPDATE `device_cohort` SET total_sales="+dc.getTotal_sales()+",same_month_active="+dc.getSame_month_active()
//						+",same_month_act_rate="+dc.getSamemonth_act_rate()+",period='"+dc.getPeriod()+"',till_now_act_rate="+ dc.getTillnow_act_rate() 
//						+ ",`jan`=" + dc.getJanTotCnt() + ",`feb`=" + dc.getFebTotCnt() + ",`mar`= "+ dc.getMarTotCnt() + ",`apr`=" + dc.getAprTotCnt()
//						+ ",`may`=" + dc.getMayTotCnt()+ ",`jun`=" + dc.getJunTotCnt() + ",`jul`="+ dc.getJulTotCnt()+",`aug`="+dc.getAugTotCnt()+",`sep`="
//						+dc.getSepTotCnt()+",`oct`="+dc.getOctTotCnt()+",`nov`="+dc.getNovTotCnt()+",`dec`="+dc.getDecTotCnt()+",`registered`="+dc.getRegCnt()+",`un_identified_registered`="+dc.getUnIdentifiedCnt()+",`un_identified_activated`="+dc.getUnIdentifiedActivatedCnt()+",`total_inactive`="+dc.getTotal_inactive()
//						+",month_no="+dc.getMonth_no()+" where `year`="+year+" AND `month`='"+dc.getMonth()+"' AND sales_channel='"+dc.getSales_ch()+"';";
//			
//				log.info("Update Query - " + updateQuery);
//				int updateStatus = db_connection_iris.prepareStatement(updateQuery).executeUpdate();
//				if (updateStatus > 0)
//					log.info("Row updated for yr: " + year+" : "+dc.getMonth());
//				else
//					log.info("Row not updated for yr: " + year+" : "+dc.getMonth());
//			} catch (Exception e1) {
//				log.error("Exception in updateDeviceCohort - " + e1.getLocalizedMessage());
//			}
//		
//		}
//		}
//		
//		}
//	}
}
