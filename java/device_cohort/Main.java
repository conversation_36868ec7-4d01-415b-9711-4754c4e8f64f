package device_cohort;

import java.io.FileInputStream;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.Year;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.Set;

import org.apache.log4j.Logger;
import org.apache.log4j.PropertyConfigurator;

import dbconnection.Connection_DB;
import pojo.DeviceCohort;
import pojo.MonthDetail;
import pojo.PeriodDetail;
import pojo.PeriodWiseDetail;

public class Main {

	static final Logger log = Logger.getLogger(Main.class);
	private static Connection_DB con_iris;
	private static Connection_DB con_niom;
	public static void main(String[] args) {
		PropertyConfigurator.configure("device_cohort-log4j.properties");
		log.info("Started");
		Connection db_connection_iris = null;
		Connection db_connection_niom = null;

		try {
			Properties prop = new Properties();
			int monthno=1;
			InputStream input;
			try {
				input = new FileInputStream("Config.properties");
				prop.load(input);
			} catch (FileNotFoundException e1) {
				log.error(e1.getLocalizedMessage());
			} catch (IOException e) {
				log.error(e.getLocalizedMessage());
			}

			String amplitude_url=prop.getProperty("amplitudeUrl");
			int year=Integer.parseInt(prop.getProperty("year"));
			String username=prop.getProperty("username");
			String pass=prop.getProperty("password");
			String monthArray[] = prop.getProperty("month_days").trim().split(",");
			List<String> month_days = Arrays.asList(monthArray);
			DeviceDetail devicedet= new DeviceDetail();
			ArrayList<DeviceCohort> dcList = null;

			boolean isToolRunningDaily = Boolean.parseBoolean(prop.getProperty("toolRunningDaily"));
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String start = prop.getProperty("start_date").trim();
			String end = prop.getProperty("end_date").trim();

			if (isToolRunningDaily) {
				try {
					Calendar fromCal = Calendar.getInstance();
					Calendar toCal = Calendar.getInstance();

					int dayOfYr = fromCal.get(Calendar.DAY_OF_YEAR);

					if(dayOfYr==1) { //To get previous year last day date 
						fromCal.set(Calendar.YEAR, (fromCal.get(Calendar.YEAR)-1));
						toCal.set(Calendar.YEAR, (toCal.get(Calendar.YEAR)-1));
					}

					fromCal.set(Calendar.DATE,1);
					fromCal.set(Calendar.MONTH,0);
					fromCal.set(Calendar.HOUR_OF_DAY, 0);
					fromCal.set(Calendar.MINUTE, 0);
					fromCal.set(Calendar.SECOND, 1);
					start = sdf.format(fromCal.getTime());
					log.info("st dt :"+sdf.format(fromCal.getTime()));

					toCal.set(Calendar.DAY_OF_MONTH, toCal.getActualMaximum(Calendar.DAY_OF_MONTH));
					toCal.set(Calendar.HOUR_OF_DAY, 23);
					toCal.set(Calendar.MINUTE, 59);
					toCal.set(Calendar.SECOND, 59);
					end = sdf.format(toCal.getTime());
					log.info("end dt : "+sdf.format(toCal.getTime()));



				}catch (Exception e) {
					log.error("cohort:isToolRunningDaily: "+e.getLocalizedMessage());

				}
			}
			DeviceCohort dcobj= new DeviceCohort();
			
			Map<String,Integer> dcMapList=new HashMap<String, Integer>();
			
			
			while(monthno < 12) {
				
				String startMonth = getMinDate(monthno, year);
				String endMonth = getMaxDate(monthno, year);
				log.info("Month start Date: " + startMonth);
				log.info("Month End Date: " + endMonth);
						
				con_niom = new Connection_DB(prop, "niom");
				db_connection_niom = con_niom.getConnection();
				Map<String,Integer>totalSale=devicedet.getOrdersTotalSaleList(db_connection_niom,startMonth,endMonth);
				con_iris = new Connection_DB(prop, "iris");
				db_connection_iris = con_iris.getConnection();
				String month[]= {"jan","feb","mar","apr","may","jun","jul","aug","sep","oct","nov","dec",};
				dcMapList=devicedet.getDeviceCohortList(db_connection_iris,startMonth,endMonth,monthno);
							
				ArrayList<DeviceCohort>dclist=getDeviceCohort(dcMapList,totalSale,db_connection_iris,month[monthno-1],year);
				
				Map<String,String>appdwldCount=devicedet.getAppDwldDetails(amplitude_url,startMonth,endMonth,username,pass,month_days);
				con_iris = new Connection_DB(prop, "iris");
				db_connection_iris = con_iris.getConnection();
				updateValueInTable(appdwldCount,db_connection_iris,year);
				monthno=monthno+1;
				
			}	
			log.info("Completed");
						
		}catch (Exception e) {
			log.error("cohort:Main: "+e.getLocalizedMessage());

		}
	}
	
	private static ArrayList<DeviceCohort> getDeviceCohort(Map<String, Integer> dcMapList, Map<String, Integer> totalSale, Connection db_connection_iris, String month_name, int year) {
		log.info("Entered saveDeviceCohort");
		ArrayList<DeviceCohort>dclist=new ArrayList<DeviceCohort>();
		try {
			System.out.println();

			DeviceCohort dc= new DeviceCohort();
			String month[]= {"jan","feb","mar","apr","may","jun","jul","aug","sep","oct","nov","dec"};
			String salesch[]= {"amazon","rv","walmart","others"};
			String period[]= {"monthly","quarterly","half-yearly","yearly","2-year","other"};
			String categories[]= {"registered","activated","unIdentifiedactivated","unIdentifiedregistered"};


			int totalcount=0;
			int regcnt=0;
			int unregcnt=0;
			int unidentifiedRegcnt=0;
			int unidentifiedActivateCnt=0;
			int sameMonthActivecount=0;
			String smonth_act_rate = "0";
			int activatecnt=0;
			int activationrate=0;
			int jancnt=0;
			int febcnt=0;
			int marcnt=0;
			int aprcnt=0;
			int maycnt=0;
			int juncnt=0;
			int julcnt=0;
			int augcnt=0;
			int sepcnt=0;
			int octcnt=0;
			int novcnt=0;
			int deccnt=0;
			int totCnt=0;
			String mon=month_name;
			int month_no=0;

			String per = "NA";

			for(String sch:salesch) {
				for(String perioddet:period) {
					for(String mn:month) {
						for(String cate:categories) {
							String k=sch+":"+perioddet+":"+cate+":"+mn;
							month_no=getMonthNo(mn);					
							per=perioddet;
							System.out.println(k);
							System.out.println();
							for(Entry<String, Integer> dcmap:dcMapList.entrySet()) {
								if(dcmap.getKey().equalsIgnoreCase(k.trim())) {
									String []category=k.split(":");
									if(category[2].equalsIgnoreCase("unIdentifiedregistered")) {

										unidentifiedRegcnt+=dcmap.getValue();
									}else if(category[2].equalsIgnoreCase("unIdentifiedactivated")) {

										unidentifiedActivateCnt+=dcmap.getValue();
									}else if(category[2].equalsIgnoreCase("activated")) {
										activatecnt=dcmap.getValue();
										if(mon.equalsIgnoreCase(mn)) {
											sameMonthActivecount=activatecnt;
										
										}
										
									}else if(category[2].equalsIgnoreCase("registered")) {
										int count=dcmap.getValue();
										regcnt=count;
										
								
									}
									if(category[2].equalsIgnoreCase("activated")) {
										int index=k.lastIndexOf(":");
										String mname=k.substring(index,k.length());

										if(mname.contains("jan")) {
											jancnt=dcmap.getValue();
											
											break;
										}else if(mname.contains("feb")) {
											febcnt=dcmap.getValue();
											break;
										}else if(mname.contains("mar")) {
											marcnt=dcmap.getValue();
											break;
										}else if(mname.contains("apr")) {
											aprcnt=dcmap.getValue();
											break;
										}else if(mname.contains("may")) {
											maycnt=dcmap.getValue();
											break;
										}else if(mname.contains("jun")) {
											juncnt=dcmap.getValue();
											break;
										}else if(mname.contains("jul")) {
											julcnt=dcmap.getValue();
											break;
										}else if(mname.contains("aug")) {
											augcnt=dcmap.getValue();
											break;
										}else if(mname.contains("sep")) {
											sepcnt=dcmap.getValue();
											break;
										}else if(mname.contains("oct")) {
											octcnt=dcmap.getValue();
											break;
										}else if(mname.contains("nov")) {
											novcnt=dcmap.getValue();
											break;
										}else if(mname.contains("dec")) {
											deccnt=dcmap.getValue();
											break;
										}
									}

								}
							}


						}
						for(Entry<String, Integer> sale:totalSale.entrySet()) {
							String[] salechannel=sale.getKey().split("-");
							if(salechannel[1].toLowerCase().equalsIgnoreCase(sch)) {
								totCnt=sale.getValue();
							}
						}	
						
						if (sameMonthActivecount > 0 && totCnt > 0) {
							float samemonth_act_rate = (float) ((float) sameMonthActivecount / (float) totCnt) * 100f;
							DecimalFormat df = new DecimalFormat("#.##");
							smonth_act_rate = df.format(samemonth_act_rate);
						}
						
						DeviceCohort dcObj= new DeviceCohort(sch,totCnt, mon,sameMonthActivecount, per, jancnt,febcnt, marcnt, aprcnt, 
								maycnt, juncnt, julcnt,augcnt,sepcnt, octcnt, novcnt, deccnt,unidentifiedRegcnt,unidentifiedActivateCnt,year,month_no,Float.parseFloat(smonth_act_rate),regcnt);

						dclist.add(dcObj);
						unidentifiedRegcnt=0;
						unidentifiedActivateCnt=0;
						regcnt=0;
						totalcount=0;
						regcnt=0;
						unregcnt=0;
						sameMonthActivecount=0;
						activatecnt=0;
						activationrate=0;
						jancnt=0;
						febcnt=0;
						marcnt=0;
						aprcnt=0;
						maycnt=0;
						juncnt=0;
						julcnt=0;
						augcnt=0;
						sepcnt=0;
						octcnt=0;
						novcnt=0;
						deccnt=0;
						totCnt=0;
						month_no=0;
						smonth_act_rate="0";
					}					
				}
			}

			for(DeviceCohort dcobj:dclist) {
				try {
					System.out.println();
					String insertqry="Insert into device_cohort (`year`,`month`,`sales_channel`,`total_sales`,`same_month_active`,"
							+ "`jan`,`feb`,`mar`,`apr`,`may`,`jun`,`jul`,`aug`,`sep`,`oct`,`nov`,`dec`,`un_identified_registered`,`un_identified_activated`,`period`,`month_no`,`same_month_act_rate`,`registered`)"
							+ "values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);";

					PreparedStatement stmt=db_connection_iris.prepareStatement(insertqry); 
					stmt.setInt(1,dcobj.getYear());
					stmt.setString(2,dcobj.getMonth());  
					stmt.setString(3,dcobj.getSales_ch());
					stmt.setInt(4,dcobj.getTotal_sales());
					stmt.setInt(5,dcobj.getSame_month_active());  
					stmt.setInt(6,dcobj.getJanTotCnt());
					stmt.setInt(7,dcobj.getFebTotCnt());
					stmt.setInt(8,dcobj.getMarTotCnt());  
					stmt.setInt(9,dcobj.getAprTotCnt());
					stmt.setInt(10,dcobj.getMayTotCnt());  
					stmt.setInt(11,dcobj.getJunTotCnt());
					stmt.setInt(12,dcobj.getJulTotCnt());
					stmt.setInt(13,dcobj.getAugTotCnt());  
					stmt.setInt(14,dcobj.getSepTotCnt());
					stmt.setInt(15,dcobj.getOctTotCnt());
					stmt.setInt(16,dcobj.getNovTotCnt());  
					stmt.setInt(17,dcobj.getDecTotCnt()); 
					stmt.setInt(18,dcobj.getUnindentifiedregisteredcount());
					stmt.setInt(19,dcobj.getUnidentifiedactivatecount());
					stmt.setString(20, dcobj.getPeriod());
					stmt.setInt(21, dcobj.getMonth_no());
					stmt.setFloat(22, dcobj.getSame_month_act_rate());
					stmt.setInt(23, dcobj.getRegistered());
					int i=stmt.executeUpdate();  
					if(i>0) {
						log.info("Month-"+dcobj.getMonth()+" Saleschannel-"+dcobj.getSales_ch()+" Inserted");
					}else {
						log.info("Month-"+dcobj.getMonth()+" Saleschannel-"+dcobj.getSales_ch()+" not inserted");
					}
					System.out.println();
				}catch (Exception e) {
					String updateqry="update device_cohort set total_sales=?,same_month_active=?,jan=?,feb=?,mar=?,apr=?,may=?," + 
							"jun=?,jul=?,aug=?,sep=?,`oct`=?,nov=?,`dec`=?,un_identified_registered=?,un_identified_activated=?,same_month_act_rate=?,registered=? where `year`=? and `month`=? and month_no=? and sales_channel=? and `period`=?;";
					PreparedStatement stmt=db_connection_iris.prepareStatement(updateqry);  

					stmt.setInt(1,dcobj.getTotal_sales());
					stmt.setInt(2,dcobj.getSame_month_active());  
					stmt.setInt(3,dcobj.getJanTotCnt());
					stmt.setInt(4,dcobj.getFebTotCnt());
					stmt.setInt(5,dcobj.getMarTotCnt());  
					stmt.setInt(6,dcobj.getAprTotCnt());
					stmt.setInt(7,dcobj.getMayTotCnt());  
					stmt.setInt(8,dcobj.getJunTotCnt());
					stmt.setInt(9,dcobj.getJulTotCnt());
					stmt.setInt(10,dcobj.getAugTotCnt());  
					stmt.setInt(11,dcobj.getSepTotCnt());
					stmt.setInt(12,dcobj.getOctTotCnt());
					stmt.setInt(13,dcobj.getNovTotCnt());  
					stmt.setInt(14,dcobj.getDecTotCnt()); 
					stmt.setInt(15,dcobj.getUnindentifiedregisteredcount());
					stmt.setInt(16,dcobj.getUnidentifiedactivatecount());
					stmt.setFloat(17, dcobj.getSame_month_act_rate());
					stmt.setInt(18, dcobj.getRegistered());
					stmt.setInt(19,dcobj.getYear());
					stmt.setString(20,dcobj.getMonth());  
					stmt.setInt(21, dcobj.getMonth_no());
					stmt.setString(22,dcobj.getSales_ch());
					stmt.setString(23, dcobj.getPeriod());
					System.out.println();
					int i=stmt.executeUpdate();  
					if(i>0) {
						log.info("Month-"+dcobj.getMonth()+" Saleschannel-"+dcobj.getSales_ch()+" updated");
					}else {
						log.info("Month-"+dcobj.getMonth()+" Saleschannel-"+dcobj.getSales_ch()+" not updated");
					}

				}	
			}
		}catch (Exception e) {
			log.error("Error in saveDeviceCohort: "+e.getLocalizedMessage());
		}finally {
			try {
				db_connection_iris.close();
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return dclist;

	}

	

	private static int getMonthNo(String mn) {
		log.info("Entered getMonthNo");
		int monthno=0;
		try {
			if(mn.equalsIgnoreCase("jan")) {
				monthno=1;
				
			}else if(mn.equalsIgnoreCase("feb")) {
				monthno=2;
				
			}else if(mn.equalsIgnoreCase("mar")) {
				monthno=3;
				
			}else if(mn.equalsIgnoreCase("apr")) {
				monthno=4;
				
			}else if(mn.equalsIgnoreCase("may")) {
				monthno=5;
				
			}else if(mn.equalsIgnoreCase("jun")) {
				monthno=6;
				
			}else if(mn.equalsIgnoreCase("jul")) {
				monthno=7;
				
			}else if(mn.equalsIgnoreCase("aug")) {
				monthno=8;
				
			}else if(mn.equalsIgnoreCase("sep")) {
				monthno=9;
				
			}else if(mn.equalsIgnoreCase("oct")) {
				monthno=10;
				
			}else if(mn.equalsIgnoreCase("nov")) {
				monthno=11;
				
			}else if(mn.equalsIgnoreCase("dec")) {
				monthno=12;
				
			}
			
		}catch (Exception e) {
			log.error("Error in getMonthNo"+e.getLocalizedMessage());
		}
		return monthno;
	
	}

	public static String getMinDate(int monthNumber, int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, monthNumber - 1); 
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.DATE,1);
        calendar.set(Calendar.MONTH,0);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 1);
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  
        String strDate = dateFormat.format(calendar.getTime());  
        return strDate;
    }
 

    public static String getMaxDate(int monthNumber, int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, monthNumber - 1);
        int lastDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        calendar.set(Calendar.DAY_OF_MONTH, lastDay);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
		calendar.set(Calendar.MINUTE, 59);
		calendar.set(Calendar.SECOND, 59);
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  
        String endDate = dateFormat.format(calendar.getTime());  
        return endDate;
    }
    
    private static void updateValueInTable(Map<String, String> appdwldCount, Connection db_connection_iris, int year) {
		log.info("Entered updateValueInTable");
		
			for(Entry<String, String> count:appdwldCount.entrySet()) {
				String downloadedDate=count.getKey();
				String dwldCount=count.getValue();
				String[] appCount = dwldCount.split(","); 
			
				try {
				
				String insertQuery="insert into app_downloads_count(datetime,ioscount,androidcount,year) values(?,?,?,?);";
				PreparedStatement stmt=db_connection_iris.prepareStatement(insertQuery);  
				stmt.setString(1,downloadedDate);
				stmt.setString(2,appCount[0]);  
				stmt.setString(3,appCount[1]);  
				stmt.setInt(4,year);  
				int i=stmt.executeUpdate();  
				if(i>0) {
					log.info("Count value inserted for date "+downloadedDate);
				} else {
					log.info("Count value not inserted for date "+downloadedDate);
				} 
			}catch (Exception e) {
				try {
				String update_qry="update app_downloads_count set ioscount=?, androidcount=? where year=? and datetime=?";
				PreparedStatement stmt=db_connection_iris.prepareStatement(update_qry);  
				stmt.setString(1,appCount[0]);
				stmt.setString(2,appCount[1]);  
				stmt.setInt(3,year);  
				stmt.setString(4,downloadedDate);  
				int i=stmt.executeUpdate();  
				if(i>0) {
					log.info("Count updated for date "+downloadedDate);
				} else {
					log.info("Count not updated for date "+downloadedDate);
				}
				}catch (Exception e1) {
					log.error("Exception while executing updateValueInTable : " + e1.getLocalizedMessage());
				}
		}
		
	}
    }
    
}