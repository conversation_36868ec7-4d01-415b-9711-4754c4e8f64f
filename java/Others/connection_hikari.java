package Others;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Properties;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

public class connection_hikari {

	private static HikariConfig mConfig;
	private static Connection conn = null;
	private static HikariDataSource mDataSource;

	public static void intializeConnection(Properties properties, String db) {

		String url = "NA";
		String username = "NA";
		String password = "NA";

		if (db.equals("iris")) {
			url = properties.getProperty("iris_url").trim();
			username = properties.getProperty("iris_username").trim();
			password = properties.getProperty("iris_password").trim();
		} else if (db.equals("niom")) {
			url = properties.getProperty("niom_url").trim();
			username = properties.getProperty("niom_username").trim();
			password = properties.getProperty("niom_password").trim();
		}

		int minimumIdle = Integer.parseInt(properties.getProperty("minimumidle"));
		int maximumPoolSize = Integer.parseInt(properties.getProperty("maximumpoolsize"));
		String prepStmtCacheSize = properties.getProperty("prepStmtCacheSize").trim();
		String prepStmtCacheSqlLimit = properties.getProperty("prepStmtCacheSqlLimit").trim();
		int timeout = Integer.parseInt(properties.getProperty("timeout"));
		boolean autoCommit = Boolean.parseBoolean(properties.getProperty("autocommit"));

		mConfig = new HikariConfig();
		mConfig.setDataSourceClassName("com.mysql.jdbc.jdbc2.optional.MysqlDataSource");
		mConfig.addDataSourceProperty("url", url);
		mConfig.addDataSourceProperty("user", username);
		mConfig.addDataSourceProperty("password", password);
		mConfig.setPoolName(db);
		mConfig.setMinimumIdle(minimumIdle);
	
		mConfig.setMaximumPoolSize(maximumPoolSize);
		mConfig.addDataSourceProperty("cachePrepStmts", "true");
		mConfig.addDataSourceProperty("prepStmtCacheSize", prepStmtCacheSize);
		mConfig.addDataSourceProperty("prepStmtCacheSqlLimit", prepStmtCacheSqlLimit);
		mConfig.addDataSourceProperty("useServerPrepStmts", "true");
		mConfig.setIdleTimeout(100);
		mConfig.setAutoCommit(autoCommit);
		mConfig.setConnectionTimeout(timeout);
		mDataSource = new HikariDataSource(mConfig);

		try {

			mDataSource.getJdbcUrl();
			conn = mDataSource.getConnection();
			conn.close();
		} catch (Exception sqlEx) {
			System.out.println(sqlEx);

		}
	}

	public static Connection getConnection() throws SQLException {
		return mDataSource.getConnection();
	}

}
