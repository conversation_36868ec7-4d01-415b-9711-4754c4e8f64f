#database
iris_url =***********************************************************************************************
iris_username =devcohort_tool
iris_password =1xaf7z@Bx

read_iris_url =***************************************************************************************************************************
read_iris_username =devcohort_tool
read_iris_password =1xaf7z@Bx
#
#iris_url =**************************************************************************
#iris_username =nimble
#iris_password =flamingo2010
###
#niom_url =**************************************************************************
#niom_username =nimble
#niom_password =flamingo2010


#Datajar connection 
urlDataJar =*******************************************************
dataJarUsername =devcohort_tool
dataJarPassword =1xaf7z@Bx 
 
niom_url=***********************************************************************************************
niom_username=devcohort_tool
niom_password=1xaf7z@Bx

start_date=2023-01-01 00:00:01
end_date=2023-01-15 23:59:59
# false backward date data's ; true for running daily
toolRunningDaily=true

runForPreviousYear=true

minimumidle=1
maximumpoolsize=5
prepStmtCacheSize=1000
prepStmtCacheSqlLimit=1000
timeout=500
maximumactivetime=6000
maximumconnectioncount=50
throttle=25
autocommit=true

month_days=jan:31,feb_even:29,feb_odd:28,mar:31,apr:30,may:31,jun:30,jul:31,aug:31,sep:30,oct:31,nov:30,dec:31
amplitudeUrl=https://amplitude.com/api/2/users?
username=f318ead05a214e9cc8fa5fe4d40f4c62
password=06bd70236469dd38a16ff17bddb233f1

#year on which the cohort should run
toolrunyear=2023

#Updating return date for yesterday
updateReturnDate=false
returnOrderdate=-1
#updating previous year return date

isPrevYearReturnDateUpdate=false
prevYearDateUpdate=2023 

timeZones=UTC

tableName=test_device_cohort

updateTestUser=false

run_device_cohort=true
run_hologram_pause=false
run_eseye_pause=false

# Hologram SIM activation API details
hologram_service_url=https://dashboard.hologram.io/api/1/devices/
hologram_api_password=95O09pxICBHgT9ziNgR9zIrZb7gcWc

# Email configuration
email.toaddr=<EMAIL>
email.ccaddr=<EMAIL>,<EMAIL>