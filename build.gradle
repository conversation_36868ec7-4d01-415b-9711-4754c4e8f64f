apply plugin: 'java'
apply plugin: 'eclipse'
apply plugin: 'application'
mainClassName = 'device_cohort.Main'
version = '2.0'

repositories {
    mavenCentral()
}
task fatJar(type: Jar) {
	manifest {
        attributes 'Implementation-Title': 'device_cohort',
        	'Implementation-Version': version,
        	'Main-Class': 'device_cohort.Main'
    }
    baseName = 'device_cohort'
    from { configurations.compile.collect { it.isDirectory() ? it : zipTree(it) } }
    with jar
}
sourceCompatibility = 1.8
targetCompatibility = 1.8
dependencies {
    // This dependency is exported to consumers, that is to say found on their compile classpath.

    // This dependency is used internally, and not exposed to consumers on their own compile classpath.
    implementation 'com.google.guava:guava:28.0-jre'
	compile group: 'org.json', name: 'json', version: '20140107'

    // Use JUnit test framework
    testImplementation 'junit:junit:4.12'
    
    compile group: 'commons-codec', name: 'commons-codec', version: '1.6'
    compile group: 'commons-logging', name: 'commons-logging', version: '1.1.3'
    compile group: 'mysql', name: 'mysql-connector-java', version: '8.0.33'
    compile group: 'org.apache.poi', name: 'poi-ooxml', version: '3.12'
    compile group: 'org.apache.poi', name: 'poi', version: '3.12'
    compile group: 'org.apache.poi', name: 'poi-ooxml-schemas', version: '3.12'
    compile group: 'org.apache.xmlbeans', name: 'xmlbeans', version: '3.0.2'
    compile group: 'log4j', name: 'log4j', version: '1.2.16'

    compile 'com.fasterxml.jackson.core:jackson-databind:2.16.0'

    compile 'com.sun.mail:jakarta.mail:2.0.1'
    compile 'com.sun.activation:jakarta.activation:2.0.1'

    compile 'com.zaxxer:HikariCP:4.0.3'
    
}

configurations.all {
 exclude module: 'log4j-to-slf4j'
 exclude module: 'slf4j-log4j12'
}
