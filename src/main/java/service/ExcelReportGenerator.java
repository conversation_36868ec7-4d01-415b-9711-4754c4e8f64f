package service;

import Pojo.SimReport;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

public class ExcelReportGenerator {

    static final Logger log = Logger.getLogger(ExcelReportGenerator.class);

    public ByteArrayOutputStream generateSimStatusExcel(List<SimReport> simStatusList) {

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Sim Report");

        // Create bold font style for header
        CellStyle headerCellStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerCellStyle.setFont(headerFont);
        headerCellStyle.setAlignment(CellStyle.ALIGN_CENTER);

        // Define headers
        String[] columns = {"No.", "MEID", "ICCID", "Device Id", "Current Status", "Pause Status"};
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < columns.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(columns[i]);
            cell.setCellStyle(headerCellStyle);
        }

        int rowNum = 1;
        for (SimReport sim : simStatusList) {
            Row row = sheet.createRow(rowNum++);
            row.createCell(0).setCellValue(rowNum-1);
            row.createCell(1).setCellValue(sim.getMeid());
            row.createCell(2).setCellValue(sim.getIccid());
            row.createCell(3).setCellValue(sim.getDeviceId());
            row.createCell(4).setCellValue(sim.getCurrentState());
            row.createCell(5).setCellValue(sim.getPauseStatus());
        }

        for (int i = 0; i < columns.length; i++) {
            sheet.autoSizeColumn(i);
        }

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            workbook.write(out);
        } catch (IOException e) {
            log.error("Error generating Excel: " + e.getMessage());
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                log.error("Error closing workbook: " + e.getMessage());
            }
        }

        return out;
    }
}