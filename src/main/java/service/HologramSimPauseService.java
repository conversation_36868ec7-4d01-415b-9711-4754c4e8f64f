package service;

import Pojo.SimInfo;
import Pojo.SimReport;
import com.fasterxml.jackson.databind.ObjectMapper;
import dbconnection.Connection_DB;
import org.apache.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

public class HologramSimPauseService {

    static final Logger log = Logger.getLogger(HologramSimPauseService.class);

    private static final ObjectMapper mapper = new ObjectMapper();

    private String hologramServiceUrl;

    private String hologramApiPassword;

    private long deviceCount = 0;

    private long pauseCount = 0;

    private long failedCount = 0;

    private long noChangeCount = 0;

    public void pauseSimsForHologramGateways(Properties properties) throws SQLException, ClassNotFoundException {

        log.info("Entered pauseSimsForHologramGateways...");
        List<SimInfo> simInfoList = fetchHologramSimsWithoutPlans(properties);
        List<SimReport> simReportList = new ArrayList<>();
        deviceCount = simInfoList.size();

        hologramServiceUrl = properties.getProperty("hologram_service_url");
        hologramApiPassword = properties.getProperty("hologram_api_password");

        for(SimInfo sim : simInfoList) {
            SimReport simReport = pauseHologramSim(sim.getIccid(), sim.getDeviceId());
            simReport.setMeid(sim.getMeid());

            simReportList.add(simReport);

            if(simReport.getPauseStatus().equalsIgnoreCase("Pause Success")) {
                saveHologramSimPauseHistory(simReport.getMeid(), simReport.getIccid(), simReport.getDeviceId(), properties);
            }
        }

        ExcelReportGenerator excelReportGenerator = new ExcelReportGenerator();
        ByteArrayOutputStream excelStream = excelReportGenerator.generateSimStatusExcel(simReportList);

        EmailService emailService = new EmailService();
        String emailBody = generateEmailBody(deviceCount, pauseCount, failedCount, noChangeCount);
        emailService.sendEmailWithAttachment(emailBody, excelStream, properties);
    }

    private List<SimInfo> fetchHologramSimsWithoutPlans(Properties properties) throws ClassNotFoundException {

        log.info("Entered fetchHologramSimsWithoutPlans...");

        // Get all Hologram SIMs from NIOM
        List<SimInfo> allHologramSims = fetchAllHologramSims(properties);

        if (allHologramSims.isEmpty()) {
            return allHologramSims;
        }

        // Filter out SIMs with active subscriptions using IRIS database
        return filterSimsWithoutActiveSubscriptions(allHologramSims, properties);
    }

    private List<SimInfo> fetchAllHologramSims(Properties properties) throws ClassNotFoundException {

        log.info("Entered fetchAllHologramSims...");
        List<SimInfo> simInfoList = new ArrayList<>();

        String query = "SELECT device_id, meid, sim_no FROM inventory WHERE sim_vendor = 'HOLOGRAM'";

        Connection_DB niomConnection = new Connection_DB(properties, "niom");
        try (Connection con = niomConnection.getConnection();
             PreparedStatement stmt = con.prepareStatement(query);
             ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                SimInfo sim = new SimInfo();
                sim.setMeid(rs.getString("meid"));
                sim.setIccid(rs.getString("sim_no"));
                sim.setDeviceId(rs.getString("device_id"));
                simInfoList.add(sim);
            }

        } catch (SQLException e) {
            log.error("SQL Error while fetching all Hologram SIMs: " + e.getMessage());
        }

        return simInfoList;
    }

    private List<SimInfo> filterSimsWithoutActiveSubscriptions(List<SimInfo> hologramSims, Properties properties) throws ClassNotFoundException {

        log.info("Entered filterSimsWithoutActiveSubscriptions...");
        List<SimInfo> filteredSims = new ArrayList<>();

        List<String> meidList = hologramSims.stream()
                .map(SimInfo::getMeid)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (meidList.isEmpty()) return filteredSims;

        // Get MEIDs that should be excluded (have active subscriptions or are in pause history)
        Set<String> excludedMeids = getExcludedMeids(meidList, properties);

        // Filter original list to exclude MEIDs with active plans
        filteredSims = hologramSims.stream()
                .filter(sim -> !excludedMeids.contains(sim.getMeid()))
                .collect(Collectors.toList());

        return filteredSims;
    }

    private Set<String> getExcludedMeids(List<String> meidList, Properties properties) throws ClassNotFoundException {

        log.info("Entered getExcludedMeids...");
        Set<String> excludedMeids = new HashSet<>();

        // Process in batches to avoid database query limits
        int batchSize = 1000;

        for (int i = 0; i < meidList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, meidList.size());
            List<String> batch = meidList.subList(i, endIndex);

            log.info("Processing batch " + (i/batchSize + 1) + " with " + batch.size() + " MEIDs");
            excludedMeids.addAll(getExcludedMeidsBatch(batch, properties));
        }

        return excludedMeids;
    }

    private Set<String> getExcludedMeidsBatch(List<String> meidBatch, Properties properties) throws ClassNotFoundException {

        log.info("Entered getExcludedMeidsBatch...");
        Set<String> excludedMeids = new HashSet<>();
        String placeholders = meidBatch.stream().map(m -> "?").collect(Collectors.joining(", "));

        String query =
                "SELECT DISTINCT G.meid " +
                        "FROM gateway G " +
                        "WHERE G.meid IN (" + placeholders + ") " +
                        "AND (" +
                        "  EXISTS (" +
                        "    SELECT 1 FROM all_product_subscription APS " +
                        "    WHERE APS.gateway_id = G.id " +
                        "    AND APS.subscription_status IN ('active', 'non_renewing') " +
                        "    AND APS.is_deleted = 0 " +
                        "    AND APS.is_test = 0 " +
                        "    AND APS.duplicate_subs = 0 " +
                        "  ) " +
                        "  OR EXISTS (" +
                        "    SELECT 1 FROM usergateway UG " +
                        "    JOIN user U ON U.id = UG.userid " +
                        "    JOIN all_chargebee_subscription ACS ON ACS.chargebee_id = U.chargebeeid " +
                        "    WHERE UG.gatewayid = G.id " +
                        "    AND ACS.is_migrated = 0 " +
                        "    AND ACS.duplicate_subs = 0 " +
                        "    AND ACS.is_test = 0 " +
                        "    AND ACS.is_deleted = 0 " +
                        "    AND ACS.subscription_status IN ('active', 'non_renewing') " +
                        "  )" +
                        ") " +

                        "UNION " +

                        "SELECT DISTINCT HPH.meid " +
                        "FROM hologram_pause_history HPH " +
                        "WHERE HPH.meid IN (" + placeholders + ")";

        Connection_DB irisConnection = new Connection_DB(properties, "iris");
        try (Connection con = irisConnection.getConnection();
             PreparedStatement stmt = con.prepareStatement(query)) {

            int index = 1;
            // Set parameters for first IN clause
            for (String meid : meidBatch) {
                stmt.setString(index++, meid);
            }
            // Set parameters for second IN clause (UNION part)
            for (String meid : meidBatch) {
                stmt.setString(index++, meid);
            }

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    excludedMeids.add(rs.getString("meid"));
                }
            }

        } catch (SQLException e) {
            log.error("SQL Error while getting excluded MEIDs batch: " + e.getMessage());
        }

        return excludedMeids;
    }

    private SimReport pauseHologramSim(String iccid, String deviceId) {

        log.info("Entered pauseHologramSim...");

        SimReport simReport = new SimReport();
        simReport.setDeviceId(deviceId);
        simReport.setIccid(iccid);

        try {
            // Step 1: Fetch SIM details
            Map<String, Object> simDetails = fetchDeviceDetails(deviceId);
            if (simDetails == null) {
                return updateReport(simReport, "Unknown", "Pause Failed", true);
            }

            List<Map<String, Object>> cellularList = extractCellularList(simDetails);
            if (cellularList == null) {
                log.info("Unexpected or missing 'cellular' list.");
                return updateReport(simReport, "Unknown", "Pause Failed", true);
            }

            for (Map<String, Object> simEntry : cellularList) {
                String sim = String.valueOf(simEntry.getOrDefault("sim", ""));
                log.info("Checking SIM Number: " + sim);

                if (!iccid.equals(sim)) continue;

                String state = String.valueOf(simEntry.getOrDefault("state", ""));
                log.info("SIM state :: " + state);

                if(state.contains("PAUSE")) {
                    state = "PAUSED";
                }
                switch (state) {
                    case "LIVE":
                        return pauseSim(simReport, deviceId, state);
                    case "PAUSED":
                        log.info("SIM is already paused.");
                        return updateReport(simReport, state, "Already Paused", false);
                    case "TEST-ACTIVATION":
                        log.info("SIM is in TEST mode.");
                        return updateReport(simReport, state, "In Testing", false);
                    default:
                        log.info("Unknown SIM state [" + state + "]");
                        return updateReport(simReport, state, "Failed", true);
                }
            }

            log.info("SIM ICCID not found in response.");
            return updateReport(simReport, "Unknown", "Pause Failed", true);

        } catch (Exception e) {
            log.error("Error during SIM pause: " + e.getLocalizedMessage());
            return updateReport(simReport, "Unknown", "Pause Failed", true);
        }
    }

    private Map<String, Object> fetchDeviceDetails(String deviceId) throws IOException {

        log.info("Fetching device details for deviceId: " + deviceId);
        String auth = "apikey:" + hologramApiPassword;
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());
        String url = hologramServiceUrl + deviceId;

        HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
        conn.setRequestMethod("GET");
        conn.setRequestProperty("Authorization", "Basic " + encodedAuth);
        conn.setRequestProperty("Accept", "application/json");

        if (conn.getResponseCode() != 200) {
            log.info("Failed to fetch device details. Status code: " + conn.getResponseCode());
            return null;
        }

        try (InputStream is = conn.getInputStream()) {
            Map<String, Object> body = mapper.readValue(is, Map.class);
            if (body == null || !Boolean.TRUE.equals(body.get("success"))) {
                log.info("API call unsuccessful or empty.");
                return null;
            }
            return (Map<String, Object>) body.get("data");
        }
    }

    private List<Map<String, Object>> extractCellularList(Map<String, Object> data) {

        log.info("Entered into extracting cellular list from device details...");
        Object links = data.get("links");
        if (!(links instanceof Map)) return null;

        Object cellularObj = ((Map<?, ?>) links).get("cellular");
        if (!(cellularObj instanceof List)) return null;

        List<?> rawList = (List<?>) cellularObj;
        List<Map<String, Object>> result = new ArrayList<>();
        for (Object entry : rawList) {
            if (entry instanceof Map) {
                result.add((Map<String, Object>) entry);
            }
        }
        return result;
    }

    private SimReport pauseSim(SimReport simReport, String deviceId, String currentState) throws IOException {

        log.info("SIM is LIVE. Attempting pause for deviceId: " + deviceId);

        String url = hologramServiceUrl + deviceId + "/state";
        String auth = "apikey:" + hologramApiPassword;
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());

        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("state", "pause");
        String jsonBody = mapper.writeValueAsString(requestBody);

        HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Authorization", "Basic " + encodedAuth);
        conn.setRequestProperty("Content-Type", "application/json");
        conn.setDoOutput(true);

        try (OutputStream os = conn.getOutputStream()) {
            os.write(jsonBody.getBytes(StandardCharsets.UTF_8));
        }

        if (conn.getResponseCode() == 200) {
            pauseCount++;
            log.info("SIM pause successful for deviceId: " + deviceId);
            return updateReport(simReport, "PAUSED-USER", "Pause Success", false);
        } else {
            log.info("SIM pause failed. Status: " + conn.getResponseCode());
            return updateReport(simReport, currentState, "Pause Failed", true);
        }
    }

    private SimReport updateReport(SimReport report, String currentState, String pauseStatus, boolean isFailure) {

        log.info("Updating report for deviceId: " + report.getDeviceId() + " with currentState: " +
                currentState + " and pauseStatus: " + pauseStatus);
        report.setCurrentState(currentState);
        report.setPauseStatus(pauseStatus);

        if (isFailure) failedCount++;
        else if(pauseStatus.equalsIgnoreCase("Already Paused")
                || pauseStatus.equalsIgnoreCase("In Testing")) {
            noChangeCount++;
        }

        return report;
    }

    private void saveHologramSimPauseHistory(String meid, String iccid, String deviceId, Properties properties) {

        log.info("Saving hologram pause history for meid: " + meid + " and deviceId: " + deviceId);
        Connection_DB irisConnection = new Connection_DB(properties, "iris");
        try (Connection con = irisConnection.getConnection();
             PreparedStatement stmt = con.prepareStatement("INSERT INTO hologram_pause_history (meid, sim_no, device_id) VALUES (?, ?, ?)")) {

            stmt.setString(1, meid);
            stmt.setString(2, iccid);
            stmt.setString(3, deviceId);

            stmt.executeUpdate();

        } catch (Exception e) {
            log.error("Error occurred while saving hologram pause history: SQL Error: " + e.getLocalizedMessage());
        }
    }

    private String generateEmailBody(long deviceCount, long pauseCount, long failedCount, long noChangeCount) {

        return "<html>" +
                "<body>" +
                "<p>Hello Team,</p>" +
                "<p>Please find attached the latest <strong>Hologram SIM Pause Report</strong>.</p>" +
                "<p><strong>Summary:</strong></p>" +
                "<ul>" +
                "<li><strong>Total Devices Processed:</strong> " + deviceCount + "</li>" +
                "<li><strong>Paused Devices:</strong> " + pauseCount + "</li>" +
                "<li><strong>Failed Devices:</strong> " + failedCount + "</li>" +
                "<li><strong>No Change in Status:</strong> " + noChangeCount + "</li>" +
                "</ul>" +
                "<p>Thanks,</p>" +
                "</body>" +
                "</html>";
    }
}