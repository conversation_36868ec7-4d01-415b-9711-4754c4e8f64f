package service;

import jakarta.activation.DataHandler;
import jakarta.activation.DataSource;
import jakarta.mail.Message;
import jakarta.mail.MessagingException;
import jakarta.mail.Multipart;
import jakarta.mail.Session;
import jakarta.mail.Transport;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeBodyPart;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.internet.MimeMultipart;
import jakarta.mail.util.ByteArrayDataSource;
import org.apache.log4j.Logger;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;

public class EmailService {

    static final Logger log = Logger.getLogger(EmailService.class);

    private static final String EMAIL_HOST = "email-smtp.us-west-2.amazonaws.com";
    private static final String EMAIL_HOST_USER = "AKIATYPWHWD6LU4GNLAP";
    private static final String USERNAME = "<EMAIL>";
    private static final String EMAIL_HOST_PASSWORD = "BPT4pFA+3txhGk1u4L8AgskU7iEPiaT00qPS+Q2Il09D";

    public void sendEmailWithAttachment(String body, ByteArrayOutputStream attachmentStream, Properties configProps) {

        Properties props = new Properties();
        props.put("mail.transport.protocol", "smtps");
        props.put("mail.smtp.auth", "true");
        props.setProperty("mail.smtp.port", "587");
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.smtp.starttls.required", "true");
        props.put("mail.smtp.ssl.protocols", "TLSv1.2");

        String toAddressProp = configProps.getProperty("email.toaddr");
        String[] toEmails = (toAddressProp != null && !toAddressProp.trim().isEmpty()) ? toAddressProp.split(",") : new String[0];
        String ccProp = configProps.getProperty("email.ccaddr");
        String[] ccEmails = (ccProp != null && !ccProp.trim().isEmpty()) ? ccProp.split(",") : new String[0];

        Session session = Session.getInstance(props);

        try {
            Message message = new MimeMessage(session);
            message.setFrom(new InternetAddress(USERNAME));

            for (String toEmail : toEmails) {
                message.addRecipient(Message.RecipientType.TO, new InternetAddress(toEmail));
            }

            for (String cc : ccEmails) {
                message.addRecipients(Message.RecipientType.CC, InternetAddress.parse(cc));
            }

            message.setSubject("Hologram SIM Pause Report");

            // Email body
            MimeBodyPart textPart = new MimeBodyPart();
            textPart.setContent(body, "text/html; charset=utf-8");

            // Attachment from stream
            MimeBodyPart attachmentPart = new MimeBodyPart();
            DataSource source = new ByteArrayDataSource(
                    new ByteArrayInputStream(attachmentStream.toByteArray()),
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            );
            attachmentPart.setDataHandler(new DataHandler(source));
            attachmentPart.setFileName("Hologram-SIM-PAUSE-REPORT.xlsx");

            Multipart multipart = new MimeMultipart();
            multipart.addBodyPart(textPart);
            multipart.addBodyPart(attachmentPart);

            message.setContent(multipart);

            // Connect and send via Transport
            Transport transport = session.getTransport();
            transport.connect(EMAIL_HOST, EMAIL_HOST_USER, EMAIL_HOST_PASSWORD);
            transport.sendMessage(message, message.getAllRecipients());
            transport.close();
            log.info("Email sent successfully");
        } catch (MessagingException | IOException e) {
            log.error("Failed to send email: " + e.getLocalizedMessage());
        }
    }
}