package Pojo;

public class DeviceCohort {

	private int total_sales = 0;

	private int same_month_active = 0;

	private float same_month_act_rate = 0;

	private String month = "";

	private int janTotCnt = 0;

	private int febTotCnt = 0;

	private int marTotCnt = 0;

	private int aprTotCnt = 0;

	private int mayTotCnt = 0;

	private int junTotCnt = 0;

	private int julTotCnt = 0;

	private int augTotCnt = 0;

	private int sepTotCnt = 0;

	private int octTotCnt = 0;

	private int novTotCnt = 0;

	private int decTotCnt = 0;

	private String sales_ch = "NA";

	private int year = 0;

	private int month_no = 0;

	private String period = "NA";

	private int registered = 0;

	private int unidentifiedactivatecount = 0;

	private int unindentifiedregisteredcount = 0;

	private int samemonthactivatedcount = 0;

	private long mtype_id = 0;

	private int is_trial = 0;

	private int is_bundle = 0;

	public int getTotal_sales() {
		return total_sales;
	}

	public void setTotal_sales(int total_sales) {
		this.total_sales = total_sales;
	}

	public String getMonth() {
		return month;
	}

	public void setMonth(String month) {
		this.month = month;
	}

	public int getJanTotCnt() {
		return janTotCnt;
	}

	public void setJanTotCnt(int janTotCnt) {
		this.janTotCnt = janTotCnt;
	}

	public int getFebTotCnt() {
		return febTotCnt;
	}

	public void setFebTotCnt(int febTotCnt) {
		this.febTotCnt = febTotCnt;
	}

	public int getMarTotCnt() {
		return marTotCnt;
	}

	public void setMarTotCnt(int marTotCnt) {
		this.marTotCnt = marTotCnt;
	}

	public int getAprTotCnt() {
		return aprTotCnt;
	}

	public void setAprTotCnt(int aprTotCnt) {
		this.aprTotCnt = aprTotCnt;
	}

	public int getMayTotCnt() {
		return mayTotCnt;
	}

	public void setMayTotCnt(int mayTotCnt) {
		this.mayTotCnt = mayTotCnt;
	}

	public int getJunTotCnt() {
		return junTotCnt;
	}

	public void setJunTotCnt(int junTotCnt) {
		this.junTotCnt = junTotCnt;
	}

	public int getJulTotCnt() {
		return julTotCnt;
	}

	public void setJulTotCnt(int julTotCnt) {
		this.julTotCnt = julTotCnt;
	}

	public int getAugTotCnt() {
		return augTotCnt;
	}

	public void setAugTotCnt(int augTotCnt) {
		this.augTotCnt = augTotCnt;
	}

	public int getSepTotCnt() {
		return sepTotCnt;
	}

	public void setSepTotCnt(int sepTotCnt) {
		this.sepTotCnt = sepTotCnt;
	}

	public int getOctTotCnt() {
		return octTotCnt;
	}

	public void setOctTotCnt(int octTotCnt) {
		this.octTotCnt = octTotCnt;
	}

	public int getNovTotCnt() {
		return novTotCnt;
	}

	public void setNovTotCnt(int novTotCnt) {
		this.novTotCnt = novTotCnt;
	}

	public int getDecTotCnt() {
		return decTotCnt;
	}

	public void setDecTotCnt(int decTotCnt) {
		this.decTotCnt = decTotCnt;
	}

	public String getSales_ch() {
		return sales_ch;
	}

	public void setSales_ch(String sales_ch) {
		this.sales_ch = sales_ch;
	}

	public int getYear() {
		return year;
	}

	public void setYear(int year) {
		this.year = year;
	}

	public int getMonth_no() {
		return month_no;
	}

	public void setMonth_no(int month_no) {
		this.month_no = month_no;
	}

	public String getPeriod() {
		return period;
	}

	public void setPeriod(String period) {
		this.period = period;
	}

	public int getSame_month_active() {
		return same_month_active;
	}

	public void setSame_month_active(int same_month_active) {
		this.same_month_active = same_month_active;
	}

	public int getUnidentifiedactivatecount() {
		return unidentifiedactivatecount;
	}

	public void setUnidentifiedactivatecount(int unidentifiedactivatecount) {
		this.unidentifiedactivatecount = unidentifiedactivatecount;
	}

	public int getUnindentifiedregisteredcount() {
		return unindentifiedregisteredcount;
	}

	public void setUnindentifiedregisteredcount(int unindentifiedregisteredcount) {
		this.unindentifiedregisteredcount = unindentifiedregisteredcount;
	}

	public float getSame_month_act_rate() {
		return same_month_act_rate;
	}

	public void setSame_month_act_rate(float same_month_act_rate) {
		this.same_month_act_rate = same_month_act_rate;
	}

	public int getRegistered() {
		return registered;
	}

	public void setRegistered(int registered) {
		this.registered = registered;
	}

	public int getSamemonthactivatedcount() {
		return samemonthactivatedcount;
	}

	public void setSamemonthactivatedcount(int samemonthactivatedcount) {
		this.samemonthactivatedcount = samemonthactivatedcount;
	}

	public long getMtype_id() {
		return mtype_id;
	}

	public void setMtype_id(long mtype_id) {
		this.mtype_id = mtype_id;
	}

	public int getIs_trial() {
		return is_trial;
	}

	public void setIs_trial(int is_trial) {
		this.is_trial = is_trial;
	}

	public int getIs_bundle() {
		return is_bundle;
	}

	public void setIs_bundle(int is_bundle) {
		this.is_bundle = is_bundle;
	}

	public DeviceCohort() {
		super();
	}

	public DeviceCohort(String sales_ch, int total_sale, String monthname, int same_month_active, String period,
			int janCnt, int febCnt, int marCnt, int aprCnt, int mayCnt, int junCnt, int julCnt, int augCnt, int sepCnt,
			int octCnt, int novCnt, int decCnt, int unidentifiedReg, int unidentifiedActivate, int year, int month_no,
			float sameMonthActRate, int registeredcnt, int samemonthactivatecount, long mtype, int is_trial,
			int is_bundle) {
		super();

		this.sales_ch = sales_ch.trim();
		this.total_sales = total_sale;
		this.month = monthname;
		this.same_month_active = same_month_active;
		this.period = period;
		this.janTotCnt = janCnt;
		this.febTotCnt = febCnt;
		this.marTotCnt = marCnt;
		this.aprTotCnt = aprCnt;
		this.mayTotCnt = mayCnt;
		this.junTotCnt = junCnt;
		this.julTotCnt = julCnt;
		this.augTotCnt = augCnt;
		this.sepTotCnt = sepCnt;
		this.octTotCnt = octCnt;
		this.novTotCnt = novCnt;
		this.decTotCnt = decCnt;
		this.unindentifiedregisteredcount = unidentifiedReg;
		this.unidentifiedactivatecount = unidentifiedActivate;
		this.month_no = month_no;
		this.year = year;
		this.same_month_act_rate = sameMonthActRate;
		this.registered = registeredcnt;
		this.samemonthactivatedcount = samemonthactivatecount;
		this.mtype_id = mtype;
		this.is_trial = is_trial;
		this.is_bundle = is_bundle;
	}

}
