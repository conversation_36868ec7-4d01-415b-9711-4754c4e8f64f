package device_cohort;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;

import org.apache.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;

import Pojo.DeviceCohort;
import Pojo.PeriodDetail;
import Pojo.ReturnOrdersModel;

public class DeviceDetail {
	static final Logger log = Logger.getLogger(DeviceDetail.class);

	public ArrayList<PeriodDetail> getRegisteredList(Connection db_connection_iris, String startMonthDate,
			String endMonthDate, int monthNo) {
		log.info("Entered getRegisteredList");
		ArrayList<PeriodDetail> periodDetailList = new ArrayList<PeriodDetail>();

		try {
			String selectRegisteredQry = "SELECT COUNT(DS.user_id) AS count_userid,DATE_FORMAT(DS.instal_date,'%b')AS mn,S.sales_channel,DS.first_period "
					+ "FROM saas_order_detail S JOIN device_subscription DS ON S.user_id=DS.user_id WHERE S.category='registered'"
					+ "AND S.order_date BETWEEN ? AND ? GROUP BY mn,S.sales_channel,DS.first_period ORDER BY mn ;";

			log.info("RegisteredQuery: " + selectRegisteredQry);
			PreparedStatement pstmt = null;
			pstmt = db_connection_iris.prepareStatement(selectRegisteredQry);
			pstmt.setString(1, startMonthDate);
			pstmt.setString(2, endMonthDate);
			ResultSet rs = pstmt.executeQuery();
			while (rs.next()) {
				PeriodDetail periodDetail = new PeriodDetail();
				String saleschannel = rs.getString("sales_channel").toLowerCase();
				String firstperiod = rs.getString("first_period").toLowerCase();
				int count = Integer.parseInt(rs.getString("count_userid"));
				String monthregistered = rs.getString("mn");

				if (saleschannel.equalsIgnoreCase("NA")) {
					saleschannel = "others";
				}

				if (firstperiod.equalsIgnoreCase("NA")) {
					firstperiod = "others";
				}
				periodDetail.setSaleschannel(saleschannel);
				periodDetail.setFirst_period(firstperiod);
				periodDetail.setCount(count);
				periodDetail.setMonth_no(monthNo);
				periodDetail.setMonthname(monthregistered);
				periodDetailList.add(periodDetail);
			}

		} catch (Exception e) {
			log.error("Error in getRegisteredList" + e.getLocalizedMessage());
		}
		return periodDetailList;
	}

	private String getMonthName(String date) {
		int monthno = 0;
		String mname = "NA";
		log.info("Entered into getMonthName");
		try {
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
			Date formattedDate = dateFormat.parse(date);
			monthno = Integer.parseInt(new SimpleDateFormat("MM").format(formattedDate));
			String[] monthNames = { "January", "February", "March", "April", "May", "June", "July", "August",
					"September", "October", "November", "December" };
			mname = monthNames[monthno - 1];

		} catch (Exception e) {
			log.error("Exception in getMonthName : " + e.getLocalizedMessage());
		}

		return mname;
	}

	public ArrayList<PeriodDetail> getUnidentifiedRegisteredList(Connection db_connection_iris, String startMonthDate,
			String endMonthDate, int monthno) {
		log.info("Entered getUnidentifiedRegisteredList");
		ArrayList<PeriodDetail> periodDetailList = new ArrayList<PeriodDetail>();
		PeriodDetail periodDetail = new PeriodDetail();
		try {
			String selectRegisteredQry = "SELECT COUNT(DS.user_id) AS count_userid,DATE_FORMAT(DS.instal_date,'%b')AS mn,S.sales_channel,DS.first_period \r\n"
					+ "FROM saas_order_detail S JOIN device_subscription DS ON S.user_id=DS.user_id WHERE S.category='unIdentifiedregistered'\r\n"
					+ "AND S.order_date BETWEEN ? AND ? GROUP BY mn,S.sales_channel,DS.first_period ORDER BY mn ;";

			log.info("RegisteredQuery: " + selectRegisteredQry);
			PreparedStatement pstmt = null;
			pstmt = db_connection_iris.prepareStatement(selectRegisteredQry);
			pstmt.setString(1, startMonthDate);
			pstmt.setString(2, endMonthDate);
			ResultSet rs = pstmt.executeQuery();
			while (rs.next()) {
				String saleschannel = rs.getString("sales_channel");
				String firstperiod = rs.getString("first_period");
				int count = Integer.parseInt(rs.getString("count_userid"));
				String monthregistered = rs.getString("mn");

				if (saleschannel.equalsIgnoreCase("NA")) {
					saleschannel = "others";
				}

				if (firstperiod.equalsIgnoreCase("NA")) {
					firstperiod = "others";
				}

				periodDetail.setSaleschannel(saleschannel);
				periodDetail.setFirst_period(firstperiod);
				periodDetail.setCount(count);
				periodDetail.setMonth_no(monthno);
				periodDetail.setMonthname(monthregistered);
				periodDetailList.add(periodDetail);

			}

		} catch (Exception e) {
			log.error("Error in getUnidentifiedRegisteredList" + e.getLocalizedMessage());
		}
		return periodDetailList;
	}

	public ArrayList<PeriodDetail> getUnidentifiedActivatedList(Connection db_connection_iris, String startMonthDate,
			String endMonthDate, int monthno) {
		log.info("Entered getUnidentifiedRegisteredList");
		ArrayList<PeriodDetail> periodDetailList = new ArrayList<PeriodDetail>();
		PeriodDetail periodDetail = new PeriodDetail();
		try {
			String selectRegisteredQry = "SELECT COUNT(DS.user_id) AS count_userid,DATE_FORMAT(DS.instal_date,'%b')AS mn,S.sales_channel,DS.first_period \r\n"
					+ "FROM saas_order_detail S JOIN device_subscription DS ON S.user_id=DS.user_id WHERE S.category='unIdentifiedactivated'\r\n"
					+ "AND S.order_date BETWEEN ? AND ? GROUP BY mn,S.sales_channel,DS.first_period ORDER BY mn ;";

			log.info("RegisteredQuery: " + selectRegisteredQry);
			PreparedStatement pstmt = null;
			pstmt = db_connection_iris.prepareStatement(selectRegisteredQry);
			pstmt.setString(1, startMonthDate);
			pstmt.setString(2, endMonthDate);
			ResultSet rs = pstmt.executeQuery();
			while (rs.next()) {
				String saleschannel = rs.getString("sales_channel");
				String firstperiod = rs.getString("first_period");
				int count = Integer.parseInt(rs.getString("count_userid"));
				String monthregistered = rs.getString("mn");

				if (saleschannel.equalsIgnoreCase("NA")) {
					saleschannel = "others";
				}

				if (firstperiod.equalsIgnoreCase("NA")) {
					firstperiod = "others";
				}

				periodDetail.setSaleschannel(saleschannel);
				periodDetail.setFirst_period(firstperiod);
				periodDetail.setCount(count);
				periodDetail.setMonth_no(monthno);
				periodDetail.setMonthname(monthregistered);
				periodDetailList.add(periodDetail);
			}

		} catch (Exception e) {
			log.error("Error in getUnidentifiedRegisteredList" + e.getLocalizedMessage());
		}
		return periodDetailList;
	}

	public DeviceCohort getMonthCount(ArrayList<PeriodDetail> deviceCohortList) {
		log.info("Entered getMonthCount");
		try {
			String saleschannel = "NA";
			Set<String> saleschannelList = new HashSet<>();
			for (PeriodDetail det : deviceCohortList) {
				saleschannel = det.getSaleschannel();

				saleschannelList.add(saleschannel);
			}

		} catch (Exception e) {
			log.error("Error in getMonthCount" + e.getLocalizedMessage());
		}
		return null;
	}

	public Map<String, Integer> getDeviceCohortList(Connection db_connection_iris, String startMonth, String endMonth,
			int monthno) {
		log.info("Entered getDeviceCohortList");
		Map<String, Integer> dcList = new HashMap<String, Integer>();
		ArrayList<PeriodDetail> periodDetailList = new ArrayList<PeriodDetail>();

		Set<String> salesch = new HashSet<>();
		Set<String> perioddet = new HashSet<>();
		Set<String> categoryDetail = new HashSet<>();
		Map<String, Integer> totalSale = new HashMap<String, Integer>();

		int cnt = 0;
		int unidentifiedcnt = 0;
		int unidentifiedIncrement = 1;
		int same_month_active_count = 0;

		try {
			// String select_qry="(SELECT DATE_FORMAT(SO.subscription_date,'%b') AS
			// install_month,instal_date,SO.sales_channel,DS.user_id,DS.gateway_id,
			// category,\r\n" +
			// "LOWER(first_period) AS first_period,COUNT(SO.id) AS cnt FROM
			// `saas_order_detail` SO JOIN gateway g ON g.meid=SO.meid LEFT JOIN\r\n" +
			// " device_subscription DS ON g.id=DS.gateway_id WHERE SO.order_date BETWEEN
			// '2023-04-01 00:00:01' AND '2023-04-30 23:59:59' " +
			// "AND category ='activated' GROUP BY
			// DS.user_id,MONTH(SO.subscription_date),SO.sales_channel,category ORDER BY
			// sales_channel); ";
			String select_qry = "SELECT DATE_FORMAT(g.installed_date,'%b') AS install_month,instal_date,SO.sales_channel,DS.user_id,DS.gateway_id, category,\r\n"
					+ "LOWER(first_period) AS first_period,COUNT(SO.id) AS cnt FROM `saas_order_detail` SO JOIN gateway g ON g.meid=SO.meid LEFT JOIN device_subscription DS \r\n"
					+ "ON g.id=DS.gateway_id WHERE SO.order_date BETWEEN ? AND ? AND g.installed_date=DS.instal_date AND category LIKE '%registered%' GROUP BY DS.user_id,MONTH(instal_date),\r\n"
					+ "SO.sales_channel,category UNION \r\n"
					+ "(SELECT DATE_FORMAT(SO.subscription_date,'%b') AS install_month,instal_date,SO.sales_channel,DS.user_id,DS.gateway_id, category,\r\n"
					+ "LOWER(first_period) AS first_period,COUNT(SO.id) AS cnt FROM `saas_order_detail` SO JOIN gateway g ON g.meid=SO.meid LEFT JOIN \r\n"
					+ " device_subscription DS ON g.id=DS.gateway_id WHERE SO.order_date BETWEEN ? AND ? \r\n"
					+ "AND category LIKE '%activated%' GROUP BY DS.user_id,MONTH(SO.subscription_date),SO.sales_channel,category);";
			log.info("SelectQuery: " + select_qry);
			PreparedStatement pstmt = null;
			pstmt = db_connection_iris.prepareStatement(select_qry);
			pstmt.setString(1, startMonth);
			pstmt.setString(2, endMonth);
			pstmt.setString(3, startMonth);
			pstmt.setString(4, endMonth);
			ResultSet rs = pstmt.executeQuery();
			while (rs.next()) {
				PeriodDetail pdetail = new PeriodDetail();
				String monthName = rs.getString("install_month");
				String saleschannel = rs.getString("sales_channel");
				int count = Integer.parseInt(rs.getString("cnt"));
				String category = rs.getString("category");
				String period = rs.getString("first_period");
				if (saleschannel.equalsIgnoreCase("NA")) {
					saleschannel = "others";
				}

				if (monthName == null) {
					continue;

				}
				if (period == null || period.equalsIgnoreCase("NA")) {
					period = "others";

				}

				pdetail.setMonthname(monthName);
				pdetail.setSaleschannel(saleschannel);
				pdetail.setCount(count);
				pdetail.setCategory(category);
				pdetail.setFirst_period(period);
				periodDetailList.add(pdetail);
				String key = saleschannel + ":" + period + ":" + category + ":" + monthName;
				categoryDetail.add(category);
				salesch.add(saleschannel);
				perioddet.add(period);
				if (!totalSale.containsKey(key)) {
					totalSale.put(key, count);

				} else {
					int value = totalSale.get(key);
					totalSale.put(key, count + value);
				}

			}
		} catch (Exception e) {
			log.error("Error in getDeviceCohortList" + e.getLocalizedMessage());
		}
		return totalSale;
	}

	public Map<String, String> getAppDwldDetails(String amplitude_url, String start, String end, String username,
			String pass, List<String> month_days) {
		log.info("Entered getAppDwldDetails:");
		Map<String, String> appdwldCount = new HashMap<String, String>();
		try {
			String startDate = getDateFormat(start);
			String[] splitted_stDate = startDate.split("#");
			String endDate = getDateFormat(end);
			String[] splitted_enddate = endDate.split("#");
			String response = getHttpResponse(amplitude_url, splitted_stDate[0], splitted_enddate[0], username, pass);
			JSONObject jsonObj = new JSONObject(response);
			JSONObject dataObject = jsonObj.getJSONObject("data");
			JSONArray dateArray = dataObject.getJSONArray("xValues");

			JSONArray dwldCounts = dataObject.getJSONArray("series");
			JSONArray iosCounts = (JSONArray) dwldCounts.get(0);
			JSONArray androidCounts = (JSONArray) dwldCounts.get(1);
			for (int i = 0; i < dateArray.length(); i++) {
				for (int j = 0; j < iosCounts.length(); j++) {
					String date = dateArray.get(i).toString();
					int ioscnt = (int) iosCounts.get(i);
					int androidcnt = (int) androidCounts.get(i);
					appdwldCount.put(date, ioscnt + "," + androidcnt);
					break;
				}

			}
		} catch (Exception e) {
			log.error("Error in getAppDwldDetails:" + e.getLocalizedMessage());
		}
		return appdwldCount;
	}

	private String getDateFormat(String date) {
		log.info("Entered getDateFormat");
		String formatteddate = "NA";
		try {
			Date processDate = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").parse(date);
			Calendar cal = Calendar.getInstance();
			cal.setTime(processDate);
			String month = String.valueOf((cal.get(Calendar.MONTH)) + 1);
			String year = String.valueOf(cal.get(Calendar.YEAR));
			String day = String.valueOf(cal.get(Calendar.DATE));
			if (month.length() != 2) {
				month = "0" + month;
			}

			if (day.length() != 2) {
				day = "0" + day;
			}
			formatteddate = year + month + day + "#" + month;
		} catch (Exception e) {
			log.error("Error in getDateFormat:" + e.getLocalizedMessage());
		}
		return formatteddate;
	}

	public static String getHttpResponse(String url, String startdate, String enddate, String username,
			String password) {
		log.info("Getting the Http Response Object...!");
		try {
			String urlParams = "start=" + startdate + "&end=" + enddate + "&m=new&g=platform";
			url = url + urlParams;
			URL _webServiceURL = new URL(url);
			HttpURLConnection conn = (HttpURLConnection) _webServiceURL.openConnection();

			conn.setRequestMethod("GET");

			StringBuffer response = new StringBuffer();
			String inline = "";

			String userpass = username + ":" + password;

			String basicAuth = "Basic " + new String(Base64.getEncoder().encode(userpass.getBytes()));

			conn.setRequestProperty("Authorization", basicAuth);

			BufferedReader in = null;
			int responseCode = conn.getResponseCode();
			if (responseCode >= 400) {

				in = new BufferedReader(new InputStreamReader(conn.getErrorStream(), "UTF-8"));
				while ((inline = in.readLine()) != null) {
					response.append(inline);
					response.append("\n");
				}

				in.close();
			} else {
				in = new BufferedReader(new InputStreamReader(conn.getInputStream()));

				while ((inline = in.readLine()) != null) {
					response.append(inline);
				}
				in.close();
			}

			log.info("Returning the Http Response Object...!");
			return response.toString();
		} catch (Exception e) {
			log.error(e.getLocalizedMessage());
		}
		return null;
	}

	public Map<String, Integer> getOrdersTotalSaleList(Connection db_connection_niom, String start, String end,
			String returnStartDate, String returnEnddate, String timezone) {

		log.info("Entered getOrdersTotalSaleList - timezone : " + timezone);
		Map<String, Integer> totalDeviceCohortList = new HashMap<String, Integer>();
		String order_bundle_qry = "", refund_bundle_qry = "";

		try {
			String get_bundle_qry = "SELECT `externalsku`, `startdate`,`enddate` FROM `externalsubscriptionconfig` WHERE startdate > '"
					+ start + "' OR enddate > '"+start+"';";

			PreparedStatement pstmt = null;
			pstmt = db_connection_niom.prepareStatement(get_bundle_qry);

			ResultSet rs = pstmt.executeQuery();

			int rowCount = 0;
			while (rs.next()) {
				if (rowCount == 0) {
					order_bundle_qry = " CASE ";
					refund_bundle_qry = " CASE ";
				}
				rowCount++;

				String externalsku = rs.getString("externalsku");
				String startdate = rs.getString("startdate");
				String enddate = rs.getString("enddate");

				if (timezone.equalsIgnoreCase("PST")) {
					order_bundle_qry += " WHEN o.order_sku = '" + externalsku
							+ "' AND (o.order_date BETWEEN CONVERT_TZ('" + startdate
							+ "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ('" + enddate
							+ "', 'America/Los_Angeles', 'UTC')) THEN 1 ";
					refund_bundle_qry += " WHEN api.sku = '" + externalsku + "' AND (o.datetime BETWEEN CONVERT_TZ('"
							+ startdate + "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ('" + enddate
							+ "', 'America/Los_Angeles', 'UTC')) THEN 1 ";
				} else {
					order_bundle_qry += " WHEN o.order_sku = '" + externalsku + "' AND o.order_date BETWEEN '"
							+ startdate + "' AND '" + enddate + "' THEN 1 ";
					refund_bundle_qry += " WHEN api.sku = '" + externalsku + "' AND o.datetime BETWEEN '" + startdate
							+ "' AND '" + enddate + "' THEN 1 ";
				}
			}

			order_bundle_qry += " ELSE okd.is_bundle END ";
			refund_bundle_qry += " ELSE okd.is_bundle END ";
		} catch (Exception e) {
			log.error("Error in getBundle date" + e.getLocalizedMessage());
		}

		try {
			String select_query = "";
			if (order_bundle_qry.trim().isEmpty()) {
				order_bundle_qry = " okd.is_bundle ";
				refund_bundle_qry = " okd.is_bundle ";
			}

			if (timezone.equalsIgnoreCase("PST")) {
				select_query = "SELECT order_month, sales_channel, SUM(non_bundle_qty) AS non_bundle_qty, SUM(bundle_qty) AS bundle_qty, mtype_id, mn FROM ( ( SELECT order_month, sales_channel, IF(is_bundle=1, SUM(order_quantity) - SUM(refund_quantity), 0) AS bundle_qty, IF(is_bundle=0, SUM(order_quantity) - SUM(refund_quantity), 0) AS non_bundle_qty, mtype_id, mn FROM ( ( SELECT DATE_FORMAT(CONVERT_TZ(o.`order_date`, 'UTC', 'America/Los_Angeles'), '%b') AS order_month, 'amazon' AS sales_channel, o.order_quantity AS order_quantity, 0 AS refund_quantity, okd.mtype_id, MONTH(CONVERT_TZ(o.`order_date`, 'UTC', 'America/Los_Angeles')) AS mn, o.order_sku AS sku, o.order_id, CONVERT_TZ(o.`order_date`, 'UTC', 'America/Los_Angeles'), "
						+ order_bundle_qry
						+ " AS is_bundle FROM `order_details` o JOIN order_sku_details okd ON okd.sku = o.order_sku WHERE (o.`order_date` BETWEEN CONVERT_TZ('"
						+ start + "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ('" + end
						+ "', 'America/Los_Angeles', 'UTC')) AND o.order_acc_typeid = '5' AND o.order_status != 'cancelled' AND o.is_test = 0 AND okd.product_category = 'waggle' AND okd.mtype_id != 0 GROUP BY order_month, mtype_id, is_bundle, sku, order_id, order_date ) UNION ALL ( SELECT DATE_FORMAT(CONVERT_TZ(o.`datetime`, 'UTC', 'America/Los_Angeles'), '%b') AS order_month, 'amazon' AS sales_channel, 0 AS order_quantity, api.quantity AS refund_quantity, okd.mtype_id, MONTH(CONVERT_TZ(o.`datetime`, 'UTC', 'America/Los_Angeles')) AS mn, api.sku AS sku, api.order_id, CONVERT_TZ(o.`datetime`, 'UTC', 'America/Los_Angeles') AS order_date, "
						+ refund_bundle_qry
						+ " AS is_bundle FROM niom.orders o JOIN `amazon_payment_info` api ON api.order_id = o.external_order_id JOIN order_sku_details okd ON okd.sku = api.sku WHERE (o.`datetime` BETWEEN CONVERT_TZ('"
						+ start + "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ('" + end
						+ "', 'America/Los_Angeles', 'UTC')) AND o.is_test = 0 AND okd.product_category = 'waggle' AND okd.mtype_id != 0 AND api.market_place LIKE 'amazon.%' AND is_refund = 1 GROUP BY order_month, mtype_id, is_bundle, sku, order_id, order_date ) ) AS X GROUP BY order_month, sales_channel, mtype_id, is_bundle ) ) AS N GROUP BY order_month, sales_channel, mtype_id UNION ALL SELECT order_month, sales_channel, SUM(non_bundle_qty) AS non_bundle_qty, SUM(bundle_qty) AS bundle_qty, mtype_id, mn FROM ( ( SELECT order_month, sales_channel, SUM(non_bundle_qty) - SUM(bundle_qty) AS non_bundle_qty, SUM(bundle_qty) AS bundle_qty, mtype_id, mn FROM ( ( SELECT DATE_FORMAT(CONVERT_TZ(o.`order_date`, 'UTC', 'America/Los_Angeles'), '%b') AS order_month, oa.acc_type AS sales_channel, o.order_quantity - o.return_quantity AS non_bundle_qty, 0 AS bundle_qty, o.monitor_type_id AS mtype_id, MONTH(CONVERT_TZ(o.`order_date`, 'UTC', 'America/Los_Angeles')) AS mn FROM order_details o JOIN order_account oa ON oa.id = o.order_acc_typeid JOIN order_sku_details osd ON osd.sku = o.order_sku WHERE o.product_category = 'waggle' AND (o.`order_date` BETWEEN CONVERT_TZ('"
						+ start + "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ('" + end
						+ "', 'America/Los_Angeles', 'UTC')) AND o.monitor_type_id != 0 AND o.order_acc_typeid IN ('1', '7') AND o.order_status != 'cancelled' AND (osd.is_bundle=0 OR  (osd.is_bundle=1 AND o.product_type != 'plan' )) AND o.is_test = 0 GROUP BY order_month, oa.acc_type, o.monitor_type_id,o.order_sku,order_id ) UNION ALL ( SELECT DATE_FORMAT(CONVERT_TZ(o.`order_date`, 'UTC', 'America/Los_Angeles'), '%b') AS order_month, oa.acc_type AS sales_channel, 0 AS non_bundle_qty, o.order_quantity - o.return_quantity AS bundle_qty, o.monitor_type_id AS mtype_id, MONTH(CONVERT_TZ(o.`order_date`, 'UTC', 'America/Los_Angeles')) AS mn FROM order_details o JOIN order_account oa ON oa.id = o.order_acc_typeid JOIN order_sku_details osd ON osd.sku = o.order_sku WHERE o.product_category = 'waggle' AND (o.`order_date` BETWEEN CONVERT_TZ('"
						+ start + "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ('" + end
						+ "', 'America/Los_Angeles', 'UTC')) AND o.order_acc_typeid IN ('1', '7') AND o.order_status != 'cancelled' AND osd.is_bundle=1 AND o.is_test = 0 GROUP BY order_month, oa.acc_type, o.monitor_type_id,o.order_sku,order_id ) ) AS Z GROUP BY order_month, sales_channel, mtype_id )) AS V GROUP BY order_month, sales_channel, mtype_id;";
			} else {
				select_query = "SELECT order_month, sales_channel, SUM(non_bundle_qty) AS non_bundle_qty, SUM(bundle_qty) AS bundle_qty, mtype_id, mn FROM ( ( SELECT order_month, sales_channel, IF(is_bundle=1, SUM(order_quantity) - SUM(refund_quantity), 0) AS bundle_qty, IF(is_bundle=0, SUM(order_quantity) - SUM(refund_quantity), 0) AS non_bundle_qty, mtype_id, mn FROM ( ( SELECT DATE_FORMAT(o.`order_date`, '%b') AS order_month, 'amazon' AS sales_channel, o.order_quantity AS order_quantity, 0 AS refund_quantity, okd.mtype_id, MONTH(o.`order_date`) AS mn, o.order_sku AS sku, o.order_id, o.order_date, "
						+ order_bundle_qry
						+ " AS is_bundle FROM `order_details` o JOIN order_sku_details okd ON okd.sku = o.order_sku WHERE o.`order_date` BETWEEN '"
						+ start + "' AND '" + end
						+ "' AND o.order_acc_typeid = '5' AND o.order_status != 'cancelled' AND o.is_test = 0 AND okd.product_category = 'waggle' AND okd.mtype_id != 0 GROUP BY order_month, mtype_id, is_bundle, sku, order_id, order_date ) UNION ALL ( SELECT DATE_FORMAT(o.`datetime`, '%b') AS order_month, 'amazon' AS sales_channel, 0 AS order_quantity, api.quantity AS refund_quantity, okd.mtype_id, MONTH(o.`datetime`) AS mn, api.sku AS sku, api.order_id, o.datetime AS order_date, "
						+ refund_bundle_qry
						+ " AS is_bundle FROM niom.orders o JOIN `amazon_payment_info` api ON api.order_id = o.external_order_id JOIN order_sku_details okd ON okd.sku = api.sku WHERE o.`datetime` BETWEEN '"
						+ start + "' AND '" + end
						+ "' AND o.is_test = 0 AND okd.product_category = 'waggle' AND okd.mtype_id != 0 AND api.market_place LIKE 'amazon.%' AND is_refund = 1 GROUP BY order_month, mtype_id, is_bundle, sku, order_id, order_date ) ) AS X GROUP BY order_month, sales_channel, mtype_id, is_bundle ) ) AS V GROUP BY order_month, sales_channel, mtype_id UNION ALL SELECT order_month, sales_channel, SUM(non_bundle_qty) AS non_bundle_qty, SUM(bundle_qty) AS bundle_qty, mtype_id, mn FROM (( SELECT order_month, sales_channel, SUM(non_bundle_qty) - SUM(bundle_qty) AS non_bundle_qty, SUM(bundle_qty) AS bundle_qty, mtype_id, mn FROM ( ( SELECT DATE_FORMAT(o.`order_date`, '%b') AS order_month, oa.acc_type AS sales_channel, o.order_quantity - o.return_quantity AS non_bundle_qty, 0 AS bundle_qty, o.monitor_type_id AS mtype_id, MONTH(o.`order_date`) AS mn FROM order_details o JOIN order_account oa ON oa.id = o.order_acc_typeid JOIN order_sku_details osd ON osd.sku = o.order_sku WHERE o.product_category = 'waggle' AND o.`order_date` BETWEEN '"
						+ start + "' AND '" + end
						+ "' AND o.monitor_type_id != 0 AND o.order_acc_typeid IN ('1', '7') AND o.order_status != 'cancelled' AND (osd.is_bundle=0 OR  (osd.is_bundle=1 AND o.product_type != 'plan' )) AND o.is_test = 0 GROUP BY order_month, oa.acc_type, o.monitor_type_id,o.order_sku, order_id ) UNION ALL ( SELECT DATE_FORMAT(o.`order_date`, '%b') AS order_month, oa.acc_type AS sales_channel, 0 AS non_bundle_qty, o.order_quantity - o.return_quantity AS bundle_qty, o.monitor_type_id AS mtype_id, MONTH(o.`order_date`) AS mn FROM order_details o JOIN order_account oa ON oa.id = o.order_acc_typeid JOIN order_sku_details osd ON osd.sku = o.order_sku WHERE o.product_category = 'waggle' AND o.`order_date` BETWEEN '"
						+ start + "' AND '" + end
						+ "' AND o.order_acc_typeid IN ('1', '7') AND o.order_status != 'cancelled' AND osd.is_bundle=1 AND o.is_test = 0 GROUP BY order_month, oa.acc_type, mtype_id,o.order_sku, order_id )) AS Z GROUP BY order_month, sales_channel, mtype_id ) ) AS N GROUP BY order_month, sales_channel, mtype_id ;";
			}

			PreparedStatement pstmt = null;
			pstmt = db_connection_niom.prepareStatement(select_query);

			ResultSet rs = pstmt.executeQuery();
			while (rs.next()) {
				String salesch = rs.getString("sales_channel");
				String month = rs.getString("order_month");
//				int count = Integer.parseInt(rs.getString("cnt"));
				long mtype_id = rs.getLong("mtype_id");
				int bundle_cnt = rs.getInt("bundle_qty");
				int non_bundle_cnt = rs.getInt("non_bundle_qty");

				if (bundle_cnt > 0)
					totalDeviceCohortList.put(month + "-" + salesch + "-" + mtype_id + "-0-1", bundle_cnt);// month-saleschannel-mtype_id-intrial-bundle=count
				if (non_bundle_cnt > 0)
					totalDeviceCohortList.put(month + "-" + salesch + "-" + mtype_id + "-0-0", non_bundle_cnt);// month-saleschannel-mtype_id-intrial-bundle=count

			}
		} catch (Exception e) {
			log.error("Error in getOrdersTotalSaleList" + e.getLocalizedMessage());
		} finally {
			try {
				db_connection_niom.close();
			} catch (SQLException e) {
				log.error("Error in getOrdersTotalSaleList: Connection error " + e.getLocalizedMessage());
			}
		}
		return totalDeviceCohortList;
	}

	public Map<String, Integer> getDeviceCohortCount(Connection db_connection_iris, String startMonth, String endMonth,
			String returnStartDate, String returnEnddate, int monthno, String year, String timezone) {
		log.info("Entered getDeviceCohortCount - timezone : " + timezone);

		Set<String> salesch = new HashSet<>();
		Set<String> perioddet = new HashSet<>();
		Set<String> categoryDetail = new HashSet<>();
		Map<String, Integer> totalData = new HashMap<String, Integer>();
		ArrayList<PeriodDetail> periodDetailList = new ArrayList<PeriodDetail>();
		try {
			String getregisteredListQry = "";

			if (timezone.equalsIgnoreCase("PST")) {
				getregisteredListQry = "SELECT IF(( DATE_ADD(DATE(`purchase_date`), INTERVAL 30 DAY) >= DATE(instal_date) ), DATE_FORMAT(CONVERT_TZ(`purchase_date`, 'UTC', 'America/Los_Angeles'), '%b'), DATE_FORMAT(CONVERT_TZ(`instal_date`, 'UTC', 'America/Los_Angeles' ), '%b')) AS mname, sales_channel, COUNT(gateway_id) AS cnt, first_period, CONVERT_TZ(instal_date, 'UTC', 'America/Los_Angeles') AS instal_date, mtype_id, 0 AS is_trial, is_bundle FROM device_subscription WHERE ( purchase_date BETWEEN CONVERT_TZ('"
						+ startMonth + "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ('" + endMonth
						+ "', 'America/Los_Angeles', 'UTC') ) AND is_deleted=0 AND is_test = 0 AND YEAR(`instal_date`) >= YEAR(purchase_date) GROUP BY gateway_id, first_period, sales_channel, MONTH(CONVERT_TZ('instal_date', 'UTC', 'America/Los_Angeles')), mtype_id, is_trial, is_bundle ORDER BY mtype_id, is_trial, is_bundle; ";
			} else {
				getregisteredListQry = "SELECT IF(( DATE_ADD(DATE(`purchase_date`), INTERVAL 30 DAY) >= DATE(instal_date) ), DATE_FORMAT(`purchase_date`, '%b'), DATE_FORMAT(`instal_date`, '%b')) AS mname, sales_channel, COUNT(gateway_id) AS cnt, first_period, instal_date, mtype_id, 0 AS is_trial, is_bundle FROM device_subscription WHERE ( purchase_date BETWEEN '"
						+ startMonth + "' AND '" + endMonth
						+ "') AND is_deleted=0 AND is_test = 0 AND YEAR(`instal_date`) >= YEAR(purchase_date) GROUP BY gateway_id, first_period, sales_channel, MONTH(instal_date), mtype_id, is_trial, is_bundle ORDER BY mtype_id, is_trial, is_bundle;";
			}

			periodDetailList = getCategoryCountList("registered", getregisteredListQry, monthno, db_connection_iris,
					periodDetailList);

			String getactivatedListQuery = "";

			if (timezone.equalsIgnoreCase("PST")) {
				getactivatedListQuery = "SELECT mname, sales_channel, SUM(cnt) AS cnt, first_period, mtype_id, is_trial, is_bundle FROM ((SELECT IF(( DATE_ADD(DATE(`purchase_date`), INTERVAL 30 DAY) >= DATE( `paid_subscription_date`) ), DATE_FORMAT( CONVERT_TZ(`purchase_date`, 'UTC', 'America/Los_Angeles' ), '%b') , DATE_FORMAT(CONVERT_TZ(`paid_subscription_date`, 'UTC', 'America/Los_Angeles'), '%b' )) AS mname, sales_channel, COUNT(gateway_id) AS cnt, first_period, mtype_id, 1 AS is_trial, is_bundle FROM device_subscription WHERE ( purchase_date BETWEEN CONVERT_TZ('"
						+ startMonth + "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ( '" + endMonth
						+ "', 'America/Los_Angeles', 'UTC') ) AND YEAR(`paid_subscription_date`) >= YEAR(purchase_date) AND is_deleted = 0 AND is_test = 0 AND trial_start_date LIKE '1753%' GROUP BY gateway_id, is_bundle) UNION ALL (SELECT IF(( DATE_ADD(DATE(`purchase_date`), INTERVAL 30 DAY) >= DATE(`trial_start_date`) ), DATE_FORMAT(CONVERT_TZ(`purchase_date`, 'UTC', 'America/Los_Angeles'), '%b'), DATE_FORMAT( CONVERT_TZ(`trial_start_date`, 'UTC', 'America/Los_Angeles'), '%b' )) AS mname, sales_channel, COUNT(gateway_id) AS cnt, first_period, mtype_id, 1 AS is_trial, is_bundle FROM device_subscription WHERE ( purchase_date BETWEEN CONVERT_TZ('"
						+ startMonth + "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ( '" + endMonth
						+ "', 'America/Los_Angeles', 'UTC') ) AND order_account_status NOT IN ( 'refunded', 'recall' ) AND YEAR(`trial_start_date`) >= YEAR(purchase_date) AND is_deleted = 0 AND is_test = 0 GROUP BY gateway_id, is_bundle) UNION ALL (SELECT IF(( DATE_ADD(DATE(`purchase_date`), INTERVAL 30 DAY) >= DATE( `paid_subscription_date`) ), DATE_FORMAT( CONVERT_TZ(`purchase_date`, 'UTC', 'America/Los_Angeles' ), '%b' ) , DATE_FORMAT(CONVERT_TZ(`paid_subscription_date`, 'UTC', 'America/Los_Angeles'), '%b' )) AS mname, sales_channel, COUNT(gateway_id) AS cnt, first_period, mtype_id, 0 AS is_trial, is_bundle FROM device_subscription WHERE ( purchase_date BETWEEN CONVERT_TZ('"
						+ startMonth + "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ( '" + endMonth
						+ "', 'America/Los_Angeles', 'UTC') ) AND YEAR(`paid_subscription_date`) >= YEAR(purchase_date) AND is_deleted = 0 AND is_test = 0 GROUP BY gateway_id, is_bundle)) AS X GROUP BY X.mname, X.sales_channel, X.cnt, X.first_period, X.mtype_id, X.is_trial, is_bundle ORDER BY mtype_id, is_trial, is_bundle;";
			} else {
				getactivatedListQuery = "SELECT mname, sales_channel, SUM(cnt) AS cnt, first_period, mtype_id, is_trial, is_bundle FROM ((SELECT IF(( DATE_ADD(DATE(`purchase_date`), INTERVAL 30 DAY) >= DATE( `paid_subscription_date`) ), DATE_FORMAT(`purchase_date`, '%b'), DATE_FORMAT(`paid_subscription_date`, '%b')) AS mname, sales_channel, COUNT(gateway_id) AS cnt, first_period, mtype_id, 1 AS is_trial, is_bundle FROM device_subscription WHERE ( purchase_date BETWEEN '"
						+ startMonth + "' AND '" + endMonth
						+ "' ) AND YEAR(`paid_subscription_date`) >= YEAR(purchase_date) AND is_deleted = 0 AND is_test = 0 AND trial_start_date LIKE '1753%' GROUP BY gateway_id, is_bundle) UNION ALL (SELECT IF(( DATE_ADD(DATE(`purchase_date`), INTERVAL 30 DAY) >= DATE(`trial_start_date`) ), DATE_FORMAT(`purchase_date`, '%b'), DATE_FORMAT(`trial_start_date`, '%b')) AS mname, sales_channel, COUNT(gateway_id) AS cnt, first_period, mtype_id, 1 AS is_trial, is_bundle FROM device_subscription WHERE ( purchase_date BETWEEN '"
						+ startMonth + "' AND '" + endMonth
						+ "' ) AND order_account_status NOT IN ( 'refunded', 'recall' ) AND YEAR(`trial_start_date`) >= YEAR(purchase_date) AND is_deleted = 0 AND is_test = 0 GROUP BY gateway_id) UNION ALL (SELECT IF(( DATE_ADD(DATE(`purchase_date`), INTERVAL 30 DAY) >= DATE( `paid_subscription_date`) ), DATE_FORMAT(`purchase_date`, '%b'), DATE_FORMAT(`paid_subscription_date`, '%b')) AS mname, sales_channel, COUNT(gateway_id) AS cnt, first_period, mtype_id, 0 AS is_trial, is_bundle FROM device_subscription WHERE ( purchase_date BETWEEN '"
						+ startMonth + "' AND '" + endMonth
						+ "' ) AND YEAR(`paid_subscription_date`) >= YEAR(purchase_date) AND is_deleted = 0 AND is_test = 0 GROUP BY gateway_id, is_bundle)) AS X GROUP BY X.mname, X.sales_channel, X.first_period, X.mtype_id, X.is_trial, is_bundle ORDER BY mtype_id, is_trial, is_bundle;";
			}

			periodDetailList = getCategoryCountList("activated", getactivatedListQuery, monthno, db_connection_iris,
					periodDetailList);

			String getUnIdentifiedRegisteredListQuery = "";

			if (timezone.equalsIgnoreCase("PST")) {
				getUnIdentifiedRegisteredListQuery = "SELECT DATE_FORMAT(CONVERT_TZ(instal_date,'UTC','America/Los_Angeles'),'%b') AS mname ,sales_channel,COUNT(gateway_id) AS cnt,first_period,CONVERT_TZ(instal_date,'UTC','America/Los_Angeles') as instal_date,mtype_id, 0 AS is_trial,0 AS is_bundle FROM device_subscription WHERE (instal_date BETWEEN CONVERT_TZ('"
						+ startMonth + "','America/Los_Angeles','UTC') AND CONVERT_TZ('" + endMonth
						+ "','America/Los_Angeles','UTC') ) AND sales_channel='NA' AND purchase_date='1753-01-01 00:00:00' AND is_deleted=0 AND is_test = 0 GROUP BY gateway_id,first_period,sales_channel,MONTH(CONVERT_TZ(instal_date,'UTC','America/Los_Angeles')),mtype_id ORDER BY mtype_id;";
			} else {
				getUnIdentifiedRegisteredListQuery = "SELECT DATE_FORMAT(instal_date,'%b') AS mname ,sales_channel,COUNT(gateway_id) AS cnt,first_period,instal_date,mtype_id, 0 AS is_trial,0 AS is_bundle FROM device_subscription WHERE (instal_date BETWEEN '"
						+ startMonth + "' AND '" + endMonth
						+ "' ) AND sales_channel='NA' AND purchase_date='1753-01-01 00:00:00' AND is_deleted=0 AND is_test = 0 GROUP BY gateway_id,first_period,sales_channel,MONTH(instal_date),mtype_id ORDER BY mtype_id;";
			}

			periodDetailList = getCategoryCountList("unIdentifiedregistered", getUnIdentifiedRegisteredListQuery,
					monthno, db_connection_iris, periodDetailList);

			String getUnIdentifiedActivatedQry = "";

//			if (timezone.equalsIgnoreCase("PST")) {
//				getUnIdentifiedActivatedQry = "SELECT chargebee_id,plan_id,CONVERT_TZ(subscription_created_at,'UTC','America/Los_Angeles'),DATE_FORMAT(CONVERT_TZ(`subscription_created_at`,'UTC','America/Los_Angeles'),'%b') FROM `all_chargebee_subscription` ACS WHERE ACS.subscription_created_at BETWEEN CONVERT_TZ('"
//						+ startMonth + "','America/Los_Angeles','UTC') AND CONVERT_TZ('" + endMonth
//						+ "','America/Los_Angeles','UTC') AND ACS.chargebee_id NOT IN (SELECT chargebee_id FROM device_subscription WHERE ("
//						+ " purchase_date BETWEEN CONVERT_TZ('" + startMonth
//						+ "','America/Los_Angeles','UTC') AND CONVERT_TZ('" + endMonth
//						+ "','America/Los_Angeles','UTC') AND YEAR(CONVERT_TZ(`paid_subscription_date`,'America/Los_Angeles','UTC'))='"
//						+ year + "' AND " + " return_date NOT BETWEEN CONVERT_TZ('" + returnStartDate
//						+ "','America/Los_Angeles','UTC') AND CONVERT_TZ('" + returnEnddate
//						+ "','America/Los_Angeles','UTC'))) AND ACS.plan_id NOT IN('setup_charges','product-only','stop-subscription','stop-subscription-non','pal-trail','semi-annual-subscription','waggle-trial','vet-chat','chum','nimble-chum','pal-trial','parent-trial','angel-trial','waggle-quarterly-trial','waggle-half-yearly-trial','waggle-yearly-trial','buddy-trial','product-accessories','vetchat-monthly-unlimited','service-plan','furbit-pre-order','reseller') AND ACS.`is_deleted`=0 AND (( ACS.`billing_email` NOT LIKE '%<EMAIL>%' AND ACS.`billing_email` NOT LIKE '%nim.com%' AND ACS.`billing_email` NOT LIKE '%nimble.com%' AND ACS.`billing_email` NOT LIKE '%nimblewireless.com%' AND ACS.`billing_email`NOT LIKE '%suresh%' AND ACS.`billing_email` NOT LIKE '%viswa%' AND ACS.`billing_email` NOT LIKE '%kalai%' AND ACS.`billing_email` NOT LIKE '%sanjeevi%' AND ACS.`billing_email` NOT LIKE '%tim.com%' AND ACS.`billing_email` NOT LIKE '%<EMAIL>%' AND ACS.`billing_email` NOT LIKE '%waggle.com%' AND ACS.`billing_email` NOT LIKE '%waggle.in%' AND ACS.`billing_email` NOT LIKE '%test.com%' AND ACS.`billing_email` NOT LIKE '%vignesh%' AND ACS.`billing_email` NOT LIKE '%arunchippi%' ) OR (ACS.billing_email IN ('<EMAIL>','<EMAIL>', '<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>', '<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>'))) GROUP BY chargebee_id;";
//			} else {
//				getUnIdentifiedActivatedQry = "SELECT chargebee_id,plan_id,subscription_created_at,DATE_FORMAT(`subscription_created_at`,'%b') FROM `all_chargebee_subscription` ACS WHERE ACS.subscription_created_at BETWEEN '"
//						+ startMonth + "' AND '" + endMonth
//						+ "' AND ACS.chargebee_id NOT IN (SELECT chargebee_id FROM device_subscription WHERE ("
//						+ " purchase_date BETWEEN '" + startMonth + "' AND '" + endMonth
//						+ "' AND YEAR(`paid_subscription_date`)='" + year + "' AND " + " return_date NOT BETWEEN '"
//						+ returnStartDate + "' AND '" + returnEnddate
//						+ "')) AND ACS.plan_id NOT IN('setup_charges','product-only','stop-subscription','stop-subscription-non','pal-trail','semi-annual-subscription','waggle-trial','vet-chat','chum','nimble-chum','pal-trial','parent-trial','angel-trial','waggle-quarterly-trial','waggle-half-yearly-trial','waggle-yearly-trial','buddy-trial','product-accessories','vetchat-monthly-unlimited','service-plan','furbit-pre-order','reseller') AND ACS.`is_deleted`=0 AND (( ACS.`billing_email` NOT LIKE '%<EMAIL>%' AND ACS.`billing_email` NOT LIKE '%nim.com%' AND ACS.`billing_email` NOT LIKE '%nimble.com%' AND ACS.`billing_email` NOT LIKE '%nimblewireless.com%' AND ACS.`billing_email`NOT LIKE '%suresh%' AND ACS.`billing_email` NOT LIKE '%viswa%' AND ACS.`billing_email` NOT LIKE '%kalai%' AND ACS.`billing_email` NOT LIKE '%sanjeevi%' AND ACS.`billing_email` NOT LIKE '%tim.com%' AND ACS.`billing_email` NOT LIKE '%<EMAIL>%' AND ACS.`billing_email` NOT LIKE '%waggle.com%' AND ACS.`billing_email` NOT LIKE '%waggle.in%' AND ACS.`billing_email` NOT LIKE '%test.com%' AND ACS.`billing_email` NOT LIKE '%vignesh%' AND ACS.`billing_email` NOT LIKE '%arunchippi%' ) OR (ACS.billing_email IN ('<EMAIL>','<EMAIL>', '<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>', '<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>'))) GROUP BY chargebee_id;";
//			}
//
//			periodDetailList = getActivatedCountList("unIdentifiedactivated", getUnIdentifiedActivatedQry, startMonth,
//					endMonth, returnStartDate, returnEnddate, monthno, db_connection_iris, periodDetailList, timezone);

			if (timezone.equalsIgnoreCase("PST")) {
				getUnIdentifiedActivatedQry = "SELECT activated_month AS mname, plan_period AS first_period, SUM(cnt) AS cnt, 'others' AS sales_channel, mtype_id, is_trial, 0 AS is_bundle FROM ( ((SELECT MIN(invoice_id), DATE_FORMAT(CONVERT_TZ(`invoice_date`, 'America/Los_Angeles', 'UTC'), '%b') AS activated_month, invoice_date, i.planid, i.plan_period, COUNT(DISTINCT(chargebee_id)) AS cnt, chargebee_id,p.monitor_type AS mtype_id,1 AS is_trial , subscription_id FROM `invoicehistory_new_v2` i JOIN `plan_to_period` ptp ON i.planid = ptp.`chargebee_planid` JOIN plan p ON p.id = ptp.plan_id WHERE i.chargebee_id IN ( SELECT chargebee_id FROM `all_chargebee_subscription` ACS WHERE ACS.subscription_created_at BETWEEN CONVERT_TZ('"
						+ startMonth + "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ('" + endMonth
						+ "', 'America/Los_Angeles', 'UTC') AND ACS.chargebee_id NOT IN ( SELECT chargebee_id FROM device_subscription WHERE purchase_date NOT LIKE '1753%' AND paid_subscription_date BETWEEN CONVERT_TZ('"
						+ startMonth + "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ('" + endMonth
						+ "', 'America/Los_Angeles', 'UTC') AND mtype_id = 1 AND is_deleted=0) AND ACS.plan_id NOT IN ( 'setup_charges', 'product-only', 'stop-subscription', 'stop-subscription-non', 'pal-trail', 'semi-annual-subscription', 'waggle-trial', 'vet-chat', 'chum', 'nimble-chum', 'pal-trial', 'parent-trial', 'angel-trial', 'waggle-quarterly-trial', 'waggle-half-yearly-trial', 'waggle-yearly-trial', 'buddy-trial', 'product-accessories', 'vetchat-monthly-unlimited', 'service-plan', 'furbit-pre-order', 'reseller' ) AND ACS.`is_deleted` = 0 AND ACS.is_test=0 GROUP BY chargebee_id ) AND invoice_date BETWEEN CONVERT_TZ('"
						+ startMonth + "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ('" + endMonth
						+ "', 'America/Los_Angeles', 'UTC') AND i.planid NOT IN ( 'vet-chat', 'chum', 'nimble-chum', 'pal-trial', 'parent-trial', 'angel-trial', 'waggle-quarterly-trial', 'waggle-half-yearly-trial', 'waggle-yearly-trial', 'buddy-trial', 'product-only', 'product-accessories', 'furbit-pre-order', 'stop-subscription', 'stop-subscription-non', 'service-plan', 'waggle-trial', 'pal-trail', 'chum-plus-monthly', 'buddy-plus-trail', 'reseller' ) AND p.monitor_type =1 GROUP BY chargebee_id,mtype_id) UNION ALL (SELECT MIN(invoice_id), DATE_FORMAT(CONVERT_TZ(`invoice_date`, 'America/Los_Angeles', 'UTC'), '%b') AS activated_month, invoice_date, i.planid, i.plan_period, COUNT(DISTINCT(subscription_id)) AS cnt, chargebee_id,p.monitor_type AS mtype_id,1 AS is_trial, subscription_id FROM `invoicehistory_new_v2` i JOIN `plan_to_period` ptp ON i.planid = ptp.`chargebee_planid` JOIN plan p ON p.id = ptp.plan_id WHERE p.monitor_type !=1 AND i.subscription_id IN ( SELECT subscription_id FROM `all_product_subscription` APS WHERE APS.subscription_created_at BETWEEN CONVERT_TZ('"
						+ startMonth + "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ('" + endMonth
						+ "', 'America/Los_Angeles', 'UTC') AND APS.gateway_id NOT IN ( SELECT gateway_id FROM device_subscription WHERE purchase_date NOT LIKE '1753%' AND paid_subscription_date BETWEEN CONVERT_TZ('"
						+ startMonth + "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ('" + endMonth
						+ "', 'America/Los_Angeles', 'UTC') AND trial_start_date LIKE '1753%' AND is_deleted=0) AND APS.`is_deleted` = 0 AND APS.is_test=0 AND APS.trial_start LIKE '1753%' GROUP BY subscription_id,monitor_type ) AND invoice_date BETWEEN CONVERT_TZ('"
						+ startMonth + "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ('" + endMonth
						+ "', 'America/Los_Angeles', 'UTC') GROUP BY subscription_id,mtype_id) UNION ALL (SELECT 0 AS invoice_id, DATE_FORMAT(CONVERT_TZ(`trial_start`, 'America/Los_Angeles', 'UTC'), '%b') AS activated_month, trial_start, APS.plan_id, APS.plan_period, COUNT(DISTINCT(gateway_id)) AS cnt, chargebee_id,APS.monitor_type AS mtype_id,1 AS is_trial, subscription_id FROM `all_product_subscription` APS WHERE APS.subscription_created_at BETWEEN CONVERT_TZ('"
						+ startMonth + "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ('" + endMonth
						+ "', 'America/Los_Angeles', 'UTC') AND APS.gateway_id NOT IN ( SELECT gateway_id FROM device_subscription WHERE purchase_date NOT LIKE '1753%' AND trial_start_date BETWEEN CONVERT_TZ('"
						+ startMonth + "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ('" + endMonth
						+ "', 'America/Los_Angeles', 'UTC') AND is_deleted=0) AND APS.`is_deleted` = 0 AND APS.is_test=0 AND APS.trial_start NOT LIKE '1753%' GROUP BY subscription_id,mtype_id ) ) UNION ALL ((SELECT MIN(invoice_id), DATE_FORMAT(CONVERT_TZ(`invoice_date`, 'America/Los_Angeles', 'UTC'), '%b') AS activated_month, invoice_date, i.planid, i.plan_period, COUNT(DISTINCT(chargebee_id)) AS cnt, chargebee_id,p.monitor_type AS mtype_id,0 AS is_trial , subscription_id FROM `invoicehistory_new_v2` i JOIN `plan_to_period` ptp ON i.planid = ptp.`chargebee_planid` JOIN plan p ON p.id = ptp.plan_id WHERE i.chargebee_id IN ( SELECT chargebee_id FROM `all_chargebee_subscription` ACS WHERE ACS.subscription_created_at BETWEEN CONVERT_TZ('"
						+ startMonth + "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ('" + endMonth
						+ "', 'America/Los_Angeles', 'UTC') AND ACS.chargebee_id NOT IN ( SELECT chargebee_id FROM device_subscription WHERE purchase_date NOT LIKE '1753%' AND paid_subscription_date BETWEEN CONVERT_TZ('"
						+ startMonth + "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ('" + endMonth
						+ "', 'America/Los_Angeles', 'UTC') AND mtype_id = 1 AND is_deleted=0) AND ACS.plan_id NOT IN ( 'setup_charges', 'product-only', 'stop-subscription', 'stop-subscription-non', 'pal-trail', 'semi-annual-subscription', 'waggle-trial', 'vet-chat', 'chum', 'nimble-chum', 'pal-trial', 'parent-trial', 'angel-trial', 'waggle-quarterly-trial', 'waggle-half-yearly-trial', 'waggle-yearly-trial', 'buddy-trial', 'product-accessories', 'vetchat-monthly-unlimited', 'service-plan', 'furbit-pre-order', 'reseller' ) AND ACS.`is_deleted` = 0 AND ACS.is_test=0 GROUP BY chargebee_id ) AND invoice_date BETWEEN CONVERT_TZ('"
						+ startMonth + "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ('" + endMonth
						+ "', 'America/Los_Angeles', 'UTC') AND i.planid NOT IN ( 'vet-chat', 'chum', 'nimble-chum', 'pal-trial', 'parent-trial', 'angel-trial', 'waggle-quarterly-trial', 'waggle-half-yearly-trial', 'waggle-yearly-trial', 'buddy-trial', 'product-only', 'product-accessories', 'furbit-pre-order', 'stop-subscription', 'stop-subscription-non', 'service-plan', 'waggle-trial', 'pal-trail', 'chum-plus-monthly', 'buddy-plus-trail', 'reseller' ) AND p.monitor_type =1 GROUP BY chargebee_id,mtype_id) UNION ALL (SELECT MIN(invoice_id), DATE_FORMAT(CONVERT_TZ(`invoice_date`, 'America/Los_Angeles', 'UTC'), '%b') AS activated_month, invoice_date, i.planid, i.plan_period, COUNT(DISTINCT(subscription_id)) AS cnt, chargebee_id,p.monitor_type AS mtype_id,0 AS is_trial, subscription_id FROM `invoicehistory_new_v2` i JOIN `plan_to_period` ptp ON i.planid = ptp.`chargebee_planid` JOIN plan p ON p.id = ptp.plan_id WHERE p.monitor_type !=1 AND i.subscription_id IN ( SELECT subscription_id FROM `all_product_subscription` APS WHERE APS.subscription_created_at BETWEEN CONVERT_TZ('"
						+ startMonth + "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ('" + endMonth
						+ "', 'America/Los_Angeles', 'UTC') AND APS.gateway_id NOT IN ( SELECT gateway_id FROM device_subscription WHERE purchase_date NOT LIKE '1753%' AND paid_subscription_date BETWEEN CONVERT_TZ('"
						+ startMonth + "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ('" + endMonth
						+ "', 'America/Los_Angeles', 'UTC') AND is_deleted=0) AND APS.`is_deleted` = 0 AND APS.is_test=0 AND APS.trial_start LIKE '1753%' GROUP BY subscription_id,monitor_type ) AND invoice_date BETWEEN CONVERT_TZ('"
						+ startMonth + "', 'America/Los_Angeles', 'UTC') AND CONVERT_TZ('" + endMonth
						+ "', 'America/Los_Angeles', 'UTC') GROUP BY subscription_id,mtype_id) ) ) AS B GROUP BY activated_month, plan_period,mtype_id,is_trial;";
			} else if (timezone.equalsIgnoreCase("UTC")) {
				getUnIdentifiedActivatedQry = "SELECT activated_month AS mname, plan_period AS first_period, SUM(cnt) AS cnt, 'others' AS sales_channel, mtype_id, is_trial, 0 AS is_bundle FROM ( ((SELECT MIN(invoice_id), DATE_FORMAT(`invoice_date`, '%b') AS activated_month, invoice_date, i.planid, i.plan_period, COUNT(DISTINCT(chargebee_id)) AS cnt, chargebee_id,p.monitor_type AS mtype_id,1 AS is_trial , subscription_id FROM `invoicehistory_new_v2` i JOIN `plan_to_period` ptp ON i.planid = ptp.`chargebee_planid` JOIN plan p ON p.id = ptp.plan_id WHERE i.chargebee_id IN ( SELECT chargebee_id FROM `all_chargebee_subscription` ACS WHERE ACS.subscription_created_at BETWEEN '"
						+ startMonth + "' AND '" + endMonth
						+ "' AND ACS.chargebee_id NOT IN ( SELECT chargebee_id FROM device_subscription WHERE purchase_date NOT LIKE '1753%' AND paid_subscription_date BETWEEN '"
						+ startMonth + "' AND '" + endMonth
						+ "' AND mtype_id = 1 AND is_deleted=0) AND ACS.plan_id NOT IN ( 'setup_charges', 'product-only', 'stop-subscription', 'stop-subscription-non', 'pal-trail', 'semi-annual-subscription', 'waggle-trial', 'vet-chat', 'chum', 'nimble-chum', 'pal-trial', 'parent-trial', 'angel-trial', 'waggle-quarterly-trial', 'waggle-half-yearly-trial', 'waggle-yearly-trial', 'buddy-trial', 'product-accessories', 'vetchat-monthly-unlimited', 'service-plan', 'furbit-pre-order', 'reseller' ) AND ACS.`is_deleted` = 0 AND ACS.is_test=0 GROUP BY chargebee_id ) AND invoice_date BETWEEN '"
						+ startMonth + "' AND '" + endMonth
						+ "' AND i.planid NOT IN ( 'vet-chat', 'chum', 'nimble-chum', 'pal-trial', 'parent-trial', 'angel-trial', 'waggle-quarterly-trial', 'waggle-half-yearly-trial', 'waggle-yearly-trial', 'buddy-trial', 'product-only', 'product-accessories', 'furbit-pre-order', 'stop-subscription', 'stop-subscription-non', 'service-plan', 'waggle-trial', 'pal-trail', 'chum-plus-monthly', 'buddy-plus-trail', 'reseller' ) AND p.monitor_type =1 GROUP BY chargebee_id,mtype_id) UNION ALL (SELECT MIN(invoice_id), DATE_FORMAT(`invoice_date`, '%b') AS activated_month, invoice_date, i.planid, i.plan_period, COUNT(DISTINCT(subscription_id)) AS cnt, chargebee_id,p.monitor_type AS mtype_id,1 AS is_trial, subscription_id FROM `invoicehistory_new_v2` i JOIN `plan_to_period` ptp ON i.planid = ptp.`chargebee_planid` JOIN plan p ON p.id = ptp.plan_id WHERE p.monitor_type !=1 AND i.subscription_id IN ( SELECT subscription_id FROM `all_product_subscription` APS WHERE APS.subscription_created_at BETWEEN '"
						+ startMonth + "' AND '" + endMonth
						+ "' AND APS.gateway_id NOT IN ( SELECT gateway_id FROM device_subscription WHERE purchase_date NOT LIKE '1753%' AND paid_subscription_date BETWEEN '"
						+ startMonth + "' AND '" + endMonth
						+ "' AND trial_start_date LIKE '1753%' AND is_deleted=0) AND APS.`is_deleted` = 0 AND APS.is_test=0 AND APS.trial_start LIKE '1753%' GROUP BY subscription_id,monitor_type ) AND invoice_date BETWEEN '"
						+ startMonth + "' AND '" + endMonth
						+ "' GROUP BY subscription_id,mtype_id) UNION ALL (SELECT 0 AS invoice_id, DATE_FORMAT(`trial_start`, '%b') AS activated_month, trial_start, APS.plan_id, APS.plan_period, COUNT(DISTINCT(gateway_id)) AS cnt, chargebee_id,APS.monitor_type AS mtype_id,1 AS is_trial, subscription_id FROM `all_product_subscription` APS WHERE APS.subscription_created_at BETWEEN '"
						+ startMonth + "' AND '" + endMonth
						+ "' AND APS.gateway_id NOT IN (SELECT gateway_id FROM device_subscription WHERE purchase_date NOT LIKE '1753%' AND trial_start_date BETWEEN '"
						+ startMonth + "' AND '" + endMonth
						+ "' AND is_deleted=0) AND APS.`is_deleted` = 0 AND APS.is_test=0 AND APS.trial_start NOT LIKE '1753%' GROUP BY subscription_id,mtype_id ) ) UNION ALL ((SELECT MIN(invoice_id), DATE_FORMAT(`invoice_date`, '%b') AS activated_month, invoice_date, i.planid, i.plan_period, COUNT(DISTINCT(chargebee_id)) AS cnt, chargebee_id,p.monitor_type AS mtype_id,0 AS is_trial , subscription_id FROM `invoicehistory_new_v2` i JOIN `plan_to_period` ptp ON i.planid = ptp.`chargebee_planid` JOIN plan p ON p.id = ptp.plan_id WHERE i.chargebee_id IN ( SELECT chargebee_id FROM `all_chargebee_subscription` ACS WHERE ACS.subscription_created_at BETWEEN '"
						+ startMonth + "' AND '" + endMonth
						+ "' AND ACS.chargebee_id NOT IN ( SELECT chargebee_id FROM device_subscription WHERE purchase_date NOT LIKE '1753%' AND paid_subscription_date BETWEEN '"
						+ startMonth + "' AND '" + endMonth
						+ "' AND mtype_id = 1 AND is_deleted=0) AND ACS.plan_id NOT IN ( 'setup_charges', 'product-only', 'stop-subscription', 'stop-subscription-non', 'pal-trail', 'semi-annual-subscription', 'waggle-trial', 'vet-chat', 'chum', 'nimble-chum', 'pal-trial', 'parent-trial', 'angel-trial', 'waggle-quarterly-trial', 'waggle-half-yearly-trial', 'waggle-yearly-trial', 'buddy-trial', 'product-accessories', 'vetchat-monthly-unlimited', 'service-plan', 'furbit-pre-order', 'reseller' ) AND ACS.`is_deleted` = 0 AND ACS.is_test=0 GROUP BY chargebee_id ) AND invoice_date BETWEEN '"
						+ startMonth + "' AND '" + endMonth
						+ "' AND i.planid NOT IN ( 'vet-chat', 'chum', 'nimble-chum', 'pal-trial', 'parent-trial', 'angel-trial', 'waggle-quarterly-trial', 'waggle-half-yearly-trial', 'waggle-yearly-trial', 'buddy-trial', 'product-only', 'product-accessories', 'furbit-pre-order', 'stop-subscription', 'stop-subscription-non', 'service-plan', 'waggle-trial', 'pal-trail', 'chum-plus-monthly', 'buddy-plus-trail', 'reseller' ) AND p.monitor_type =1 GROUP BY chargebee_id,mtype_id) UNION ALL (SELECT MIN(invoice_id), DATE_FORMAT(`invoice_date`, '%b') AS activated_month, invoice_date, i.planid, i.plan_period, COUNT(DISTINCT(subscription_id)) AS cnt, chargebee_id,p.monitor_type AS mtype_id,0 AS is_trial, subscription_id FROM `invoicehistory_new_v2` i JOIN `plan_to_period` ptp ON i.planid = ptp.`chargebee_planid` JOIN plan p ON p.id = ptp.plan_id WHERE p.monitor_type !=1 AND i.subscription_id IN ( SELECT subscription_id FROM `all_product_subscription` APS WHERE APS.subscription_created_at BETWEEN '"
						+ startMonth + "' AND '" + endMonth
						+ "' AND APS.gateway_id NOT IN ( SELECT gateway_id FROM device_subscription WHERE purchase_date NOT LIKE '1753%' AND paid_subscription_date BETWEEN '"
						+ startMonth + "' AND '" + endMonth
						+ "' AND is_deleted=0) AND APS.`is_deleted` = 0 AND APS.is_test=0 AND APS.trial_start LIKE '1753%' GROUP BY subscription_id,monitor_type ) AND invoice_date BETWEEN '"
						+ startMonth + "' AND '" + endMonth
						+ "' GROUP BY subscription_id,mtype_id) ) ) AS B GROUP BY activated_month, plan_period,mtype_id,is_trial;";
			}

			periodDetailList = getCategoryCountList("unIdentifiedactivated", getUnIdentifiedActivatedQry, monthno,
					db_connection_iris, periodDetailList);

			for (PeriodDetail countitem : periodDetailList) {
				String saleschannel = countitem.getSaleschannel().toLowerCase();
				String period = countitem.getFirst_period().toLowerCase();
				String category = countitem.getCategory();
				String mname = countitem.getMonthname();
				int count = countitem.getCount();
				int mtype_id = countitem.getMtype_id();
				int is_trial = countitem.getIs_trial();
				int is_bundle = countitem.getIs_bundle();

				String key = saleschannel + ":" + period + ":" + category + ":" + mname + ":" + mtype_id + ":"
						+ is_trial + ":" + is_bundle;
				if (!totalData.containsKey(key)) {
					totalData.put(key, count);

				} else {
					int value = totalData.get(key);
					totalData.put(key, count + value);
				}
			}

		} catch (Exception e) {
			log.error("Error in getDeviceCohortList" + e.getLocalizedMessage());
		}
		return totalData;

	}

	private ArrayList<PeriodDetail> getActivatedCountList(String string, String getUnIdentifiedActivatedQry,
			String startMonth, String endMonth, String returnStartDate, String returnEnddate, int monthno,
			Connection db_connection_iris, ArrayList<PeriodDetail> periodDetailList, String timezone) {
		log.info("Entered getActivatedCountList - timezone : " + timezone);
		try {

			PreparedStatement pstmt = null;
			pstmt = db_connection_iris.prepareStatement(getUnIdentifiedActivatedQry);
			log.info(getUnIdentifiedActivatedQry);
			ResultSet rs = pstmt.executeQuery();

			log.info("getInvoiceDetails");

			while (rs.next()) {
				PeriodDetail pdetail = new PeriodDetail();
				String chargebee_id = rs.getString("chargebee_id");
				Thread.sleep(1);
				pdetail = getInvoiceDetails(chargebee_id, db_connection_iris, timezone);

				periodDetailList.add(pdetail);
			}

		} catch (Exception e) {
			log.error("Error in getActivatedCountList" + e.getLocalizedMessage());
		}
		return periodDetailList;
	}

	private PeriodDetail getInvoiceDetails(String chargebee_id, Connection db_connection_iris, String timezone) {
		PeriodDetail pdetail = new PeriodDetail();
		log.info("chargebee_id : " + chargebee_id);
		try {
			String qry = "";

			if (timezone.equalsIgnoreCase("PST")) {
				qry = "SELECT DATE_FORMAT( CONVERT_TZ(`invoice_date`,'UTC','America/Los_Angeles'),'%b') AS activated_month,invoice_date,i.planid,i.plan_period,count(distinct(chargebee_id)) as cnt FROM `invoicehistory_new_v2` i "
						+ "WHERE i.chargebee_id=? AND i.planid NOT IN('vet-chat','chum','nimble-chum','pal-trial','parent-trial','angel-trial','waggle-quarterly-trial','waggle-half-yearly-trial','waggle-yearly-trial','buddy-trial','product-only','product-accessories','furbit-pre-order',\r\n"
						+ "'stop-subscription','stop-subscription-non','service-plan','waggle-trial','pal-trail','chum-plus-monthly','buddy-plus-trail','reseller') "
						+ "ORDER BY i.invoice_date ASC LIMIT 1;";
			} else {
				qry = "SELECT DATE_FORMAT(`invoice_date`,'%b') AS activated_month,invoice_date,i.planid,i.plan_period,count(distinct(chargebee_id)) as cnt FROM `invoicehistory_new_v2` i "
						+ "WHERE i.chargebee_id=? AND i.planid NOT IN('vet-chat','chum','nimble-chum','pal-trial','parent-trial','angel-trial','waggle-quarterly-trial','waggle-half-yearly-trial','waggle-yearly-trial','buddy-trial','product-only','product-accessories','furbit-pre-order',\r\n"
						+ "'stop-subscription','stop-subscription-non','service-plan','waggle-trial','pal-trail','chum-plus-monthly','buddy-plus-trail','reseller') "
						+ "ORDER BY i.invoice_date ASC LIMIT 1;";
			}
			PreparedStatement pstmt = null;
			pstmt = db_connection_iris.prepareStatement(qry);
			pstmt.setString(1, chargebee_id);
			ResultSet rs = pstmt.executeQuery();
			String monthName = "NA";
			int count = 0;
			String period = "NA";

			while (rs.next()) {
				if (rs.getString("activated_month") != null) {
					monthName = rs.getString("activated_month");
				} else {
					monthName = "NA";
				}

				if (rs.getString("cnt") != null) {
					count = Integer.parseInt(rs.getString("cnt"));
				} else {
					count = 0;
				}

				if (rs.getString("plan_period") != null) {
					period = rs.getString("plan_period");
				} else {
					period = "NA";
				}

				pdetail.setMonthname(monthName);
				pdetail.setSaleschannel("others");
				pdetail.setCount(count);
				pdetail.setCategory("unIdentifiedactivated");
				pdetail.setFirst_period(period.toLowerCase());
			}
		} catch (Exception e) {
			log.error("Error in getInvoiceDetails" + e.getLocalizedMessage());
		}
		return pdetail;
	}

	private ArrayList<PeriodDetail> getCategoryCountList(String category, String qry, int monthno,
			Connection db_connection_iris, ArrayList<PeriodDetail> periodDetailList) {
		log.info("\n" + category + " : " + qry);

		try {

			PreparedStatement pstmt = null;
			pstmt = db_connection_iris.prepareStatement(qry);
			ResultSet rs = pstmt.executeQuery();

			while (rs.next()) {
				PeriodDetail pdetail = new PeriodDetail();
				String monthName = rs.getString("mname");
				String saleschannel = rs.getString("sales_channel");
				int count = Integer.parseInt(rs.getString("cnt"));
				String period = rs.getString("first_period");
				int is_trial = rs.getInt("is_trial");
				int mtype_id = rs.getInt("mtype_id");
				int is_bundle = rs.getInt("is_bundle");

				if (saleschannel.equalsIgnoreCase("NA")) {
					saleschannel = "others";
				}

				if (period.equalsIgnoreCase("NA")) {
					period = "others";
				}

				pdetail.setMonthname(monthName);
				pdetail.setSaleschannel(saleschannel.toLowerCase());
				pdetail.setCount(count);
				pdetail.setCategory(category);
				pdetail.setFirst_period(period);
				pdetail.setMonth_no(monthno);
				pdetail.setMtype_id(mtype_id);
				pdetail.setIs_trial(is_trial);
				pdetail.setIs_bundle(is_bundle);
				periodDetailList.add(pdetail);

				// For Registered and Unidentified registered, trial is not applicable so adding
				// the same count for trial.
				if (category.equalsIgnoreCase("registered") || category.equalsIgnoreCase("unIdentifiedregistered")) {
					PeriodDetail addTrialData = new PeriodDetail();
					addTrialData.setMonthname(monthName);
					addTrialData.setSaleschannel(saleschannel.toLowerCase());
					addTrialData.setCount(count);
					addTrialData.setCategory(category);
					addTrialData.setFirst_period(period);
					addTrialData.setMonth_no(monthno);
					addTrialData.setMtype_id(mtype_id);
					addTrialData.setIs_trial(1);
					addTrialData.setIs_bundle(is_bundle);
					periodDetailList.add(addTrialData);
				}
			}
		} catch (Exception e) {
			log.error("Error in getCategoryCountList" + e.getLocalizedMessage());
		}
		return periodDetailList;
	}

	public ArrayList<ReturnOrdersModel> getRefundedOrders(String startdate, Connection datajar_conn) {
		log.info("Entered getRefundedOrders");
		ArrayList<ReturnOrdersModel> order_details = new ArrayList<ReturnOrdersModel>();

		Main main = new Main();
		String currentDate = main.currentTime();
		try {
			String select_qry = "SELECT order_number,sale_channel,refund_date FROM `cd_return_forms` WHERE last_modified_date BETWEEN ? and ? ";

			PreparedStatement pstmt = null;
			pstmt = datajar_conn.prepareStatement(select_qry);
			log.info("selectQry: " + select_qry);
			pstmt.setString(1, startdate);
			pstmt.setString(2, currentDate);
			ResultSet rs = pstmt.executeQuery();
			String orderId = "NA";
			String refund_date = "1753-01-01 00:00:00";
			String salesChannel = "NA";
			while (rs.next()) {
				ReturnOrdersModel returnOrder = new ReturnOrdersModel();
				if (rs.getString("order_number") != null) {
					orderId = rs.getString("order_number");
				}

				if (rs.getString("sale_channel") != null) {
					salesChannel = rs.getString("sale_channel");
				}

				if (rs.getString("refund_date") != null) {
					refund_date = rs.getString("refund_date");
				}

				if (!refund_date.equalsIgnoreCase("1111-11-11")) {
					refund_date = changeDateFormat(refund_date);
				} else {
					refund_date = "1753-01-01 00:00:00";
				}

				returnOrder.setOrder_id(orderId);
				returnOrder.setSaleschannel(salesChannel);
				returnOrder.setRefund_date(refund_date);
				order_details.add(returnOrder);
			}

		} catch (Exception e) {
			log.error("Error in getRefundedOrders " + e.getLocalizedMessage());
		} finally {
			try {
				datajar_conn.close();
			} catch (SQLException e) {
				log.error("Error in getRefundedOrders::Connection error " + e.getLocalizedMessage());
			}
		}
		return order_details;
	}

	private String changeDateFormat(String refund_date) {
		log.info("Entered into changeDateFormat");
		try {
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd", Locale.US);
			SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			format.setTimeZone(TimeZone.getTimeZone("UTC"));
			Date prevDateFormat = null;
			prevDateFormat = format.parse(refund_date);
			refund_date = sdf2.format(prevDateFormat);
			return refund_date;
		} catch (Exception e) {
			log.error("Error in changeDateFormat" + e.getLocalizedMessage());
			SimpleDateFormat format = new SimpleDateFormat("dd MMMM, yyyy", Locale.US);
			SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			format.setTimeZone(TimeZone.getTimeZone("UTC"));
			Date prevDateFormat = null;
			try {
				prevDateFormat = format.parse(refund_date);
				refund_date = sdf2.format(prevDateFormat);
			} catch (ParseException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}

		}
		return refund_date;

	}

	public ArrayList<ReturnOrdersModel> getRefundedOrderDetails(Connection datajar_conn, String updatefromYear,
			boolean isToolRunningDaily, String start, String end) {

		log.info("Entered getRefundedOrderDetails: ");
		ArrayList<ReturnOrdersModel> refundedorders = new ArrayList<ReturnOrdersModel>();
		try {
			String orderId = "NA";
			String refund_date = "1753-01-01 00:00:00";
			String salesChannel = "NA";
			ResultSet rs = null;
			if (!isToolRunningDaily) {
				String select_qry = "SELECT order_number,refund_date,sale_channel FROM `cd_return_forms` WHERE YEAR(refund_date)>=? AND registration_status!='Not Registered'"
						+ "AND product NOT IN('tempcube','WaggleCam');";
				PreparedStatement pstmt = null;
				pstmt = datajar_conn.prepareStatement(select_qry);
				log.info("selectQry: " + select_qry);
				pstmt.setString(1, updatefromYear);
				rs = pstmt.executeQuery();

			} else {
				String select_qry = "SELECT order_number,refund_date,sale_channel FROM `cd_return_forms` WHERE refund_date BETWEEN ? AND ? AND registration_status!='Not Registered'"
						+ "AND product NOT IN('tempcube','WaggleCam');";
				PreparedStatement pstmt = null;
				pstmt = datajar_conn.prepareStatement(select_qry);
				log.info("selectQry: " + select_qry);
				pstmt.setString(1, start);
				pstmt.setString(2, end);
				rs = pstmt.executeQuery();
			}

			while (rs.next()) {
				ReturnOrdersModel returnOrder = new ReturnOrdersModel();
				if (rs.getString("order_number") != null) {
					orderId = rs.getString("order_number");
				}

				if (rs.getString("sale_channel") != null) {
					salesChannel = rs.getString("sale_channel");
				}

				if (rs.getString("refund_date") != null) {
					refund_date = rs.getString("refund_date");
				}

				if (!refund_date.equalsIgnoreCase("1111-11-11")) {
					refund_date = changeDateFormat(refund_date);
				} else {
					refund_date = "1753-01-01 00:00:00";
				}

				returnOrder.setOrder_id(orderId);
				returnOrder.setSaleschannel(salesChannel);
				returnOrder.setRefund_date(refund_date);
				refundedorders.add(returnOrder);
			}

		} catch (Exception e) {
			log.error("Error in getRefundedOrderDetails " + e.getLocalizedMessage());
		} finally {
			try {
				datajar_conn.close();
			} catch (SQLException e) {
				log.error("Error in getRefundedOrderDetails::Connection error " + e.getLocalizedMessage());
			}
		}
		return refundedorders;

	}

	public ArrayList<ReturnOrdersModel> getRefundedOrderBetweenDate(Connection datajar_conn, String updatefromYear) {

		log.info("Entered getRefundedOrderDetails: ");
		ArrayList<ReturnOrdersModel> refundedorders = new ArrayList<ReturnOrdersModel>();
		try {
			String select_qry = "SELECT order_number,refund_date,sale_channel FROM `cd_return_forms` WHERE YEAR(refund_date)>=? AND registration_status!='Not Registered'"
					+ "AND product NOT IN('tempcube','WaggleCam');";
			PreparedStatement pstmt = null;
			pstmt = datajar_conn.prepareStatement(select_qry);
			log.info("selectQry: " + select_qry);
			pstmt.setString(1, updatefromYear);
			ResultSet rs = pstmt.executeQuery();
			String orderId = "NA";
			String refund_date = "1753-01-01 00:00:00";
			String salesChannel = "NA";
			while (rs.next()) {
				ReturnOrdersModel returnOrder = new ReturnOrdersModel();
				if (rs.getString("order_number") != null) {
					orderId = rs.getString("order_number");
				}

				if (rs.getString("sale_channel") != null) {
					salesChannel = rs.getString("sale_channel");
				}

				if (rs.getString("refund_date") != null) {
					refund_date = rs.getString("refund_date");
				}

				if (!refund_date.equalsIgnoreCase("1111-11-11")) {
					refund_date = changeDateFormat(refund_date);
				} else {
					refund_date = "1753-01-01 00:00:00";
				}

				returnOrder.setOrder_id(orderId);
				returnOrder.setSaleschannel(salesChannel);
				returnOrder.setRefund_date(refund_date);
				refundedorders.add(returnOrder);
			}

		} catch (Exception e) {
			log.error("Error in getRefundedOrderDetails " + e.getLocalizedMessage());
		} finally {
			try {
				datajar_conn.close();
			} catch (SQLException e) {
				log.error("Error in getRefundedOrderDetails::Connection error " + e.getLocalizedMessage());
			}
		}
		return refundedorders;
	}

	public List<Long> getMonitorTypeId(Connection con) {
		log.info("Entered getMonitorTypeId");
		String query = "select id from monitortype where `enable` = 1 order by id asc;";
		List<Long> mtype = new ArrayList<Long>();

		try {
			ResultSet reviewQry = con.prepareStatement(query).executeQuery();
			while (reviewQry.next()) {
				mtype.add(reviewQry.getLong("id"));
			}
		} catch (Exception e) {
			log.error("Exception in getMonitorTypeId : " + e.getMessage());
		}
		return mtype;
	}
}
