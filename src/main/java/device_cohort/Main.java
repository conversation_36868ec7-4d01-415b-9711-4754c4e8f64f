package device_cohort;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.TimeZone;

import org.apache.log4j.Logger;
import org.apache.log4j.PropertyConfigurator;

import dbconnection.Connection_DB;
import Pojo.DeviceCohort;
import Pojo.ReturnOrdersModel;
import service.EseyeSimPauseService;
import service.HologramSimPauseService;

public class Main {

	static final Logger log = Logger.getLogger(Main.class);
	private static Connection_DB con_iris;
	private static Connection_DB con_niom;
	private static Connection_DB con_datajar;
	private static Connection_DB con_iris_read;

	public static void main(String[] args) {
		PropertyConfigurator.configure("device_cohort-log4j.properties");
		log.info("Started");

		try {
			Properties prop = new Properties();
			InputStream input;
			try {
				input = new FileInputStream("Config.properties");
				prop.load(input);
			} catch (FileNotFoundException e1) {
				log.error(e1.getLocalizedMessage());
			} catch (IOException e) {
				log.error(e.getLocalizedMessage());
			}

			if(Boolean.parseBoolean(prop.getProperty("run_hologram_pause"))) {
				log.info("Entered Hologram SIM pause");
				HologramSimPauseService pauseService = new HologramSimPauseService();
				pauseService.pauseSimsForHologramGateways(prop);
				log.info("Completed Hologram SIM pause");
			}
			else if(Boolean.parseBoolean(prop.getProperty("run_device_cohort"))) {
				log.info("Entered Device Cohort");
				boolean updateTestUsers = Boolean.valueOf(prop.getProperty("updateTestUser"));
				if (updateTestUsers)
					updateTestUser(prop, true);

				List<String> tz = Arrays.asList(prop.getProperty("timeZones").split(","));
				for (String timezone : tz)
					getCohortData(prop, timezone);

				log.info("Completed Device Cohort");
			}
			else if(Boolean.parseBoolean(prop.getProperty("run_eseye_pause"))) {
				log.info("Entered Eseye SIM pause");
				EseyeSimPauseService eseyeService = new EseyeSimPauseService();
				eseyeService.pauseSimsForEseyeGateways(prop);
				log.info("Completed Eseye SIM pause");
			}
		} catch (Exception e) {
			log.error("cohort:Main: " + e.getLocalizedMessage());

		}
	}

	private static void getCohortData(Properties prop, String timezone) {
		log.info("Entered getCohortData : " + timezone);
		String toolrunYear = "1753";

		boolean isToolRunningDaily = Boolean.parseBoolean(prop.getProperty("toolRunningDaily"));
		boolean updateReturnDate = Boolean.parseBoolean(prop.getProperty("updateReturnDate"));
		boolean runForPreviousYear = Boolean.parseBoolean(prop.getProperty("runForPreviousYear"));

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

		String start = prop.getProperty("start_date").trim();
		String end = prop.getProperty("end_date").trim();

		if (isToolRunningDaily) {
			try {
				Calendar fromCal = Calendar.getInstance();
				Calendar toCal = Calendar.getInstance();

				int dayOfMonth = fromCal.get(Calendar.DAY_OF_MONTH);
				int monthOfYear = fromCal.get(Calendar.MONTH) + 1;

				int dayOfYr = fromCal.get(Calendar.DAY_OF_YEAR);

				if (dayOfYr == 1) { // To get previous year last day date
					fromCal.set(Calendar.YEAR, (fromCal.get(Calendar.YEAR) - 1));
					toCal.set(Calendar.YEAR, (toCal.get(Calendar.YEAR) - 1));
				}

				fromCal.set(Calendar.DATE, 1);
				fromCal.set(Calendar.MONTH, 0);
				fromCal.set(Calendar.HOUR_OF_DAY, 0);
				fromCal.set(Calendar.MINUTE, 0);
				fromCal.set(Calendar.SECOND, 1);

				start = sdf.format(fromCal.getTime());
				log.info("st dt :" + sdf.format(fromCal.getTime()));

				toCal.set(Calendar.DAY_OF_MONTH, toCal.getActualMaximum(Calendar.DAY_OF_MONTH));
				toCal.set(Calendar.HOUR_OF_DAY, 23);
				toCal.set(Calendar.MINUTE, 59);
				toCal.set(Calendar.SECOND, 59);

				end = sdf.format(toCal.getTime());
				log.info("end dt : " + sdf.format(toCal.getTime()));

				toolrunYear = getCurrentYear(timezone);

				if (monthOfYear == 1 || (dayOfMonth == 2 && runForPreviousYear)) {
					// Run for Prev year
					runCohort(prop, (Integer.parseInt(toolrunYear) - 1) + "", timezone, false, false);

					// Run for current year
					runCohort(prop, toolrunYear, timezone, isToolRunningDaily, updateReturnDate);
				} else
					runCohort(prop, toolrunYear, timezone, isToolRunningDaily, updateReturnDate);
			} catch (Exception e) {
				log.error("cohort:isToolRunningDaily: " + e.getLocalizedMessage());
			}
		} else {
			toolrunYear = prop.getProperty("toolrunyear");
			runCohort(prop, toolrunYear, timezone, isToolRunningDaily, updateReturnDate);
		}

	}

	private static void runCohort(Properties prop, String toolrunYear, String timezone, boolean isToolRunningDaily,
			boolean updateReturnDate) {
		log.info("Entered run cohort - timezone : " + timezone + " , toolrunYear : " + toolrunYear
				+ " , isToolRunningDaily : " + isToolRunningDaily + " , updateReturnDate: " + updateReturnDate);
		DeviceCohort dcobj = new DeviceCohort();

		Map<String, Integer> dcMapList = new HashMap<String, Integer>();
		String startMonth = "1753-01-01 00:00:00";
		String endMonth = "1753-01-01 00:00:00";
		int monthno = 1;

		String returnStartDate = "1753-01-01 00:00:00";
		String returnEnddate = "1753-01-01 00:00:00";

		String amplitude_url = prop.getProperty("amplitudeUrl");
		String username = prop.getProperty("username");
		String pass = prop.getProperty("password");

		String monthArray[] = prop.getProperty("month_days").trim().split(",");

		List<String> month_days = Arrays.asList(monthArray);
		ArrayList<DeviceCohort> dcList = null;

		String startDate = prop.getProperty("start_date").trim();
		String endDate = prop.getProperty("end_date").trim();

		boolean isupdateReturnDatePrevYear = Boolean.parseBoolean(prop.getProperty("isPrevYearReturnDateUpdate"));

		String updatefromYear = prop.getProperty("prevYearDateUpdate");

		boolean isCurrentyear = false;

		String currentyear = getCurrentYear(timezone);
		if (currentyear.equalsIgnoreCase(toolrunYear))
			isCurrentyear = true;

		log.info(" isCurrentyear : " + isCurrentyear);

		Connection db_connection_iris = null;
		Connection db_connection_niom = null;
		Connection db_connection_iris_read = null;

		while (monthno <= 12) {
			try {
				// Get Month Start date and end date for given month number and year
				startMonth = getMinDate(monthno, Integer.parseInt(toolrunYear));
				endMonth = getMaxDate(monthno, Integer.parseInt(toolrunYear));
				log.info("Month start Date: " + startMonth);
				log.info("Month End Date: " + endMonth);

				Date startdate = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").parse(startMonth);
				Date currentDate = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").parse(currentTime());

				if (startdate.before(currentDate)) {
					if (isCurrentyear) {
						returnStartDate = getYearStartDate(monthno, Integer.parseInt(toolrunYear));
						returnEnddate = currentTime();
					} else {
						returnStartDate = getYearStartDate(monthno, Integer.parseInt(toolrunYear));
						returnEnddate = getYearEndDate(monthno, Integer.parseInt(toolrunYear));
					}

					DeviceDetail devicedet = new DeviceDetail();

					Map<String, Integer> totalSale = null;
					try {
						con_niom = new Connection_DB(prop, "niom");
						db_connection_niom = con_niom.getConnection();
						
						/* Get sale details query processing */
						totalSale = devicedet.getOrdersTotalSaleList(db_connection_niom, startMonth, endMonth,
								returnStartDate, returnEnddate, timezone);
					} catch (Exception e) {
						log.error("Exception in getOrdersTotalSaleList - " + e.getLocalizedMessage());
					} finally {
						try {
							db_connection_niom.close();
							log.info("getOrdersTotalSaleList Connections closed");
						} catch (SQLException e) {
							log.error("Exception while executing getOrdersTotalSaleList Connections close : "
									+ e.getLocalizedMessage());
						}
					}

					String month[] = { "jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov",
							"dec" };
					List<Long> mtypeList = new ArrayList<Long>();
					try {
						// Read replica
						con_iris_read = new Connection_DB(prop, "iris_read");
						db_connection_iris_read = con_iris_read.getConnection();

						/*
						 * Registered, ACtivated, UnIdentified registered and UnIdentified activated
						 * query processing.
						 */
						dcMapList = devicedet.getDeviceCohortCount(db_connection_iris_read, startMonth, endMonth,
								returnStartDate, returnEnddate, monthno, toolrunYear, timezone);

						mtypeList = devicedet.getMonitorTypeId(db_connection_iris_read);
					} catch (Exception e) {
						log.error("Exception in getDeviceCohortCount - " + e.getLocalizedMessage());
					} finally {
						try {
							db_connection_iris_read.close();
							log.info("getDeviceCohortCount Connections closed");
						} catch (SQLException e) {
							log.error("Exception while executing getDeviceCohortCount Connections close : "
									+ e.getLocalizedMessage());
						}
					}

					con_iris = new Connection_DB(prop, "iris");
					db_connection_iris = con_iris.getConnection();

					/* Cohort Calculation and update/insert into DB */
					try {
						String tableName = prop.getProperty("tableName");
						ArrayList<DeviceCohort> dclist = writeDeviceCohort(dcMapList, totalSale, db_connection_iris,
								month[monthno - 1], Integer.parseInt(toolrunYear), timezone, mtypeList, tableName);
					} catch (Exception e) {
						log.error("Exception in writeDeviceCohort - " + e.getLocalizedMessage());
					} finally {
						try {
							db_connection_iris.close();
							log.info("writeDeviceCohort Connections closed");
						} catch (SQLException e) {
							log.error("Exception while executing writeDeviceCohort Connections close : "
									+ e.getLocalizedMessage());
						}
					}

					if (timezone.equalsIgnoreCase("UTC")) {
						try {
							Map<String, String> appdwldCount = devicedet.getAppDwldDetails(amplitude_url, startMonth,
									endMonth, username, pass, month_days);

							con_iris = new Connection_DB(prop, "iris");
							db_connection_iris = con_iris.getConnection();

							// Update app downloads into DB
							updateValueInTable(appdwldCount, db_connection_iris, Integer.parseInt(toolrunYear));
						} catch (Exception e) {
							log.error("Exception in getAppDwldDetails - " + e.getLocalizedMessage());
						} finally {
							try {
								db_connection_iris.close();
								log.info("getAppDwldDetails Connections closed");
							} catch (SQLException e) {
								log.error("Exception while executing getAppDwldDetails Connections close : "
										+ e.getLocalizedMessage());
							}
						}
					}
				}
				monthno = monthno + 1;

			} catch (Exception e) {
				log.error("Error in getCohortData" + e.getLocalizedMessage());
			}
		}

		if (updateReturnDate && timezone.equalsIgnoreCase("UTC")) {
			log.info("Updating return orders in order and device subscription table");
			boolean updateReturnOrders = updateReturnOrdersDate(db_connection_niom, prop, updatefromYear,
					isupdateReturnDatePrevYear, isToolRunningDaily, startDate, endDate);
		}
	}

	private static boolean updateReturnOrdersDate(Connection niom_conn, Properties prop, String updatefromYear,
			boolean isupdateReturnDatePrevYear, boolean isToolRunningDaily, String start, String end) {
		log.info("Entered updateReturnOrdersDate");

		Connection db_connection_datajar = null;
		Connection irisconn = null;

		DeviceDetail deviceDetail = new DeviceDetail();

		try {
			if (!isupdateReturnDatePrevYear) {
				ArrayList<ReturnOrdersModel> order_id = new ArrayList<ReturnOrdersModel>();

				int day = Integer.parseInt(prop.getProperty("returnOrderdate"));
				String date = getDate(day);

				con_datajar = new Connection_DB(prop, "datajar");
				db_connection_datajar = con_datajar.getConnection();

				order_id = deviceDetail.getRefundedOrders(date, db_connection_datajar);

				con_niom = new Connection_DB(prop, "niom");
				niom_conn = con_niom.getConnection();

				updateReturnDateInOrders(order_id, niom_conn);
				con_iris = new Connection_DB(prop, "iris");

				irisconn = con_iris.getConnection();
				updateReturnDateInDeviceSubscription(order_id, irisconn);

				log.info("Yesterday Return date updated");

			} else if (isupdateReturnDatePrevYear) {

				log.info("Return date update from year:" + updatefromYear);
				try {
					ArrayList<ReturnOrdersModel> refundedOrderId = new ArrayList<ReturnOrdersModel>();

					con_datajar = new Connection_DB(prop, "datajar");
					db_connection_datajar = con_datajar.getConnection();

					refundedOrderId = deviceDetail.getRefundedOrderDetails(db_connection_datajar, updatefromYear,
							isToolRunningDaily, start, end);

					con_niom = new Connection_DB(prop, "niom");
					niom_conn = con_niom.getConnection();

					updateReturnDateInOrders(refundedOrderId, niom_conn);
					con_iris = new Connection_DB(prop, "iris");

					irisconn = con_iris.getConnection();

					updateReturnDateInDeviceSubscription(refundedOrderId, irisconn);

					log.info("Return date updated from year: " + isupdateReturnDatePrevYear);

				} catch (Exception e) {
					log.error("Error in updateReturnOrdersDate" + e.getLocalizedMessage());
				}
			} else if (isToolRunningDaily) {
				// getRefundedOrderBetweenDate
				log.info("Return date update in between dates:");
				try {
					ArrayList<ReturnOrdersModel> refundedOrderId = new ArrayList<ReturnOrdersModel>();
					con_datajar = new Connection_DB(prop, "datajar");
					db_connection_datajar = con_datajar.getConnection();
					refundedOrderId = deviceDetail.getRefundedOrderDetails(db_connection_datajar, updatefromYear,
							isToolRunningDaily, start, end);
					con_niom = new Connection_DB(prop, "niom");
					niom_conn = con_niom.getConnection();
					updateReturnDateInOrders(refundedOrderId, niom_conn);
					con_iris = new Connection_DB(prop, "iris");
					irisconn = con_iris.getConnection();
					updateReturnDateInDeviceSubscription(refundedOrderId, irisconn);
					log.info("Return date updated from year: " + isupdateReturnDatePrevYear);
				} catch (Exception e) {
					log.error("Error in updateReturnOrdersDate" + e.getLocalizedMessage());
				}

			}

		} catch (Exception e) {
			log.error("Error in updateReturnOrdersDate:: while updating in year" + updatefromYear + "error - "
					+ e.getLocalizedMessage());
		}
		return false;
	}

	private static void updateReturnDateInDeviceSubscription(ArrayList<ReturnOrdersModel> order_details,
			Connection irisconn) {

		log.info("Entered updateReturnDateInOrders");
		try {
			String updateQry = "";
			boolean isUpdated = true;
			for (ReturnOrdersModel orderdet : order_details) {
				String order_id = orderdet.getOrder_id().trim();
				String sales_channel = orderdet.getSaleschannel();
				String return_date = orderdet.getRefund_date();
				updateQry = "Update device_subscription set return_date=? where order_id=?";
				PreparedStatement pstmt = null;
				pstmt = irisconn.prepareStatement(updateQry);
				log.info("updateQry: " + updateQry);
				pstmt.setString(1, return_date);
				pstmt.setString(2, order_id);
				int updatedRows = pstmt.executeUpdate();
				if (updatedRows > 0) {
					log.info("Order_id:" + order_id + " updated");
				} else {
					log.info("Order_id:" + order_id + "not updated");
				}
			}

		} catch (Exception e) {
			log.error("Error in updateReturnDateInOrders" + e.getLocalizedMessage());
		} finally {
			try {
				irisconn.close();
			} catch (SQLException e) {
				log.error("Error in updateReturnDateInOrders::Connection error" + e.getLocalizedMessage());
			}
		}

	}

	private static void updateReturnDateInOrders(ArrayList<ReturnOrdersModel> order_details, Connection niom_conn) {
		log.info("Entered updateReturnDateInOrders");
		try {
			String updateQry = "";
			boolean isUpdated = true;
			for (ReturnOrdersModel orderdet : order_details) {
				String order_id = orderdet.getOrder_id().trim();
				String sales_channel = orderdet.getSaleschannel();
				String return_date = orderdet.getRefund_date();
				if (orderdet.getSaleschannel().equalsIgnoreCase("amazon")) {
					updateQry = "Update orders set return_date=? where external_order_id=?;";
				} else {
					updateQry = "Update orders set return_date=? where order_id=? ;";
				}
				PreparedStatement pstmt = null;
				pstmt = niom_conn.prepareStatement(updateQry);
				log.info("updateQry: " + updateQry);
				pstmt.setString(1, return_date);
				pstmt.setString(2, order_id);
				int updatedRows = pstmt.executeUpdate();
				if (updatedRows > 0) {
					log.info("Order_id:" + order_id + " updated");
				} else {
					log.info("Order_id:" + order_id + " not updated");
				}
			}

		} catch (Exception e) {
			log.error("Error in updateReturnDateInOrders" + e.getLocalizedMessage());
		} finally {
			try {
				niom_conn.close();
			} catch (SQLException e) {
				log.error("Error in updateReturnDateInOrders::Connection error" + e.getLocalizedMessage());
			}
		}
	}

	private static String getYearStartDate(int monthno, int year) {
		log.info("Entered getYearStartDate ");
		String strDate = "1753-01-01 00:00:00";
		try {
			Calendar calendar = Calendar.getInstance();
			calendar.set(Calendar.YEAR, year);
			calendar.set(Calendar.MONTH, monthno - 1);
			calendar.set(Calendar.DAY_OF_MONTH, 1);
			calendar.set(Calendar.HOUR_OF_DAY, 0);
			calendar.set(Calendar.MINUTE, 0);
			calendar.set(Calendar.SECOND, 0);
			calendar.set(Calendar.MILLISECOND, 0);
			DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			strDate = dateFormat.format(calendar.getTime());

		} catch (Exception e) {
			log.error("cohort:Main: getYearStartDate" + e.getLocalizedMessage());
		}
		return strDate;
	}

	private static String getYearEndDate(int monthno, int year) {
		log.info("Entered getYearEndDate");
		String endDate = "1753-01-01 00:00:00";
		try {
			Calendar calendar = Calendar.getInstance();
			calendar.set(Calendar.YEAR, year);
			calendar.set(Calendar.DAY_OF_MONTH, 31);
			calendar.set(Calendar.HOUR_OF_DAY, 23);
			calendar.set(Calendar.MINUTE, 59);
			calendar.set(Calendar.SECOND, 59);
			calendar.set(Calendar.MILLISECOND, 999);
			calendar.set(Calendar.MONTH, 11);
			DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			endDate = dateFormat.format(calendar.getTime());

		} catch (Exception e) {
			log.error("cohort:Main: getYearEndDate" + e.getLocalizedMessage());
		}
		return endDate;
	}

	private static ArrayList<DeviceCohort> writeDeviceCohort(Map<String, Integer> dcMapList,
			Map<String, Integer> totalSale, Connection db_connection_iris, String month_name, int year, String timezone,
			List<Long> mtypeList, String tableName) {
		log.info("Entered saveDeviceCohort - timezone : " + timezone);
		ArrayList<DeviceCohort> dclist = new ArrayList<DeviceCohort>();

		try {
			DeviceCohort dc = new DeviceCohort();
			String month[] = { "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec" };
			// String month[]= {"jan"};
			String salesch[] = { "amazon", "rv", "walmart", "others", "technorv" };
			// String salesch[]= {"amazon"};

			String period[] = { "monthly", "quarterly", "half-yearly", "yearly", "2-year", "5-Year", "others" };

			// String period[] = {"yearly"};

			String categories[] = { "activated", "registered", "unIdentifiedactivated", "unIdentifiedregistered" };

			int trial[] = { 0, 1 };
			int bundle[] = { 0, 1 };

			int totalcount = 0;
			int regcnt = 0;
			int unregcnt = 0;
			int unidentifiedRegcnt = 0;
			int unidentifiedActivateCnt = 0;
			int sameMonthRegcount = 0;
			int sameMonthActivecount = 0;
			String tot_act_rate = "0";
			int activatecnt = 0;
			int activationrate = 0;
			int jancnt = 0;
			int febcnt = 0;
			int marcnt = 0;
			int aprcnt = 0;
			int maycnt = 0;
			int juncnt = 0;
			int julcnt = 0;
			int augcnt = 0;
			int sepcnt = 0;
			int octcnt = 0;
			int novcnt = 0;
			int deccnt = 0;
			int totCnt = 0;
			String mon = month_name;
			int month_no = 0;
			String per = "NA";

			for (String sch : salesch) {
				sch = sch.toLowerCase();

				for (String periodName : period) {
					for (long mtype : mtypeList) {
						for (int in_trl : trial) {
							for (int bund : bundle) {
							for (String cate : categories) {
								for (String mn : month) {

									month_no = getMonthNo(month_name);
									per = periodName;

									String key = sch + ":" + periodName + ":" + cate + ":" + mn + ":" + mtype + ":"
											+ in_trl +":"+bund;

									if (dcMapList.containsKey(key)) {
										log.info(key);
										String[] category = key.split(":");
										if (category[2].equalsIgnoreCase("unIdentifiedregistered")) {

											unidentifiedRegcnt += dcMapList.get(key);
										} else if (category[2].equalsIgnoreCase("unIdentifiedactivated")) {

											unidentifiedActivateCnt += dcMapList.get(key);
										} else if (category[2].equalsIgnoreCase("registered")) {
											int cnt = dcMapList.get(key);

											String mname = key.split(":")[3];
											int m_no = getMonthNo(mname);

											if (m_no >= month_no) {
												regcnt += cnt;
												if (mon.equalsIgnoreCase(mn)) {
													System.out.println(mon + "-" + mn);
													sameMonthRegcount = cnt;
													System.out.println(sameMonthRegcount + "+" + mn);
												}
											}
										} else if (category[2].equalsIgnoreCase("activated")) {
											int activecnt = dcMapList.get(key);
											activatecnt += dcMapList.get(key);

											// if (category[2].equalsIgnoreCase("activated")) {
											String mname = key.split(":")[3];
											int m_no = getMonthNo(mname);

											if (m_no >= month_no) {
												if (mon.equalsIgnoreCase(mn)) {
													// sameMonthActivecount += activatecnt;
													sameMonthActivecount = dcMapList.get(key);
												}
												if (mname.contains("Jan")) {
													jancnt = dcMapList.get(key);
												} else if (mname.contains("Feb")) {
													febcnt = dcMapList.get(key);
												} else if (mname.contains("Mar")) {
													marcnt = dcMapList.get(key);
												} else if (mname.contains("Apr")) {
													aprcnt = dcMapList.get(key);
												} else if (mname.contains("May")) {
													maycnt = dcMapList.get(key);
												} else if (mname.contains("Jun")) {
													juncnt = dcMapList.get(key);
												} else if (mname.contains("Jul")) {
													julcnt = dcMapList.get(key);
												} else if (mname.contains("Aug")) {
													augcnt = dcMapList.get(key);
												} else if (mname.contains("Sep")) {
													sepcnt = dcMapList.get(key);
												} else if (mname.contains("Oct")) {
													octcnt = dcMapList.get(key);
												} else if (mname.contains("Nov")) {
													novcnt = dcMapList.get(key);
												} else if (mname.contains("Dec")) {
													deccnt = dcMapList.get(key);
												}
											}
										}
									}
								}

							}
							// Sale data processing month-sales_channel-mtype_id-in_trial-is_bundle
							for (Entry<String, Integer> sale : totalSale.entrySet()) {
								String[] salechannel = sale.getKey().split("-");
								if (salechannel[1].toLowerCase().equalsIgnoreCase(sch)
										&& salechannel[2].equalsIgnoreCase(mtype + "")
										&& salechannel[4].equalsIgnoreCase(bund + "")) {
									totCnt = sale.getValue();
								}

							}

							int totActive = jancnt + febcnt + marcnt + aprcnt + maycnt + juncnt + julcnt + augcnt
									+ sepcnt + octcnt + novcnt + deccnt;
							if (totActive > 0 && totCnt > 0) {
								float samemonth_act_rate = (float) ((float) totActive / (float) totCnt) * 100f;
								DecimalFormat df = new DecimalFormat("#.##");
								tot_act_rate = df.format(samemonth_act_rate);
							}

							// regcnt += jancnt + febcnt + marcnt + aprcnt + maycnt + juncnt + julcnt +
							// augcnt + sepcnt + octcnt
							// + novcnt + deccnt;
							DeviceCohort dcObj = new DeviceCohort(sch, totCnt, mon, sameMonthRegcount, per, jancnt,
									febcnt, marcnt, aprcnt, maycnt, juncnt, julcnt, augcnt, sepcnt, octcnt, novcnt,
									deccnt, unidentifiedRegcnt, unidentifiedActivateCnt, year, month_no,
									Float.parseFloat(tot_act_rate), regcnt, sameMonthActivecount, mtype, in_trl, bund);

							dclist.add(dcObj);
							unidentifiedRegcnt = 0;
							unidentifiedActivateCnt = 0;
							regcnt = 0;
							totalcount = 0;
							regcnt = 0;
							unregcnt = 0;
							sameMonthRegcount = 0;
							sameMonthActivecount = 0;
							activatecnt = 0;
							activationrate = 0;
							jancnt = 0;
							febcnt = 0;
							marcnt = 0;
							aprcnt = 0;
							maycnt = 0;
							juncnt = 0;
							julcnt = 0;
							augcnt = 0;
							sepcnt = 0;
							octcnt = 0;
							novcnt = 0;
							deccnt = 0;
							totCnt = 0;
							month_no = 0;
							tot_act_rate = "0";
						}
						}
					}
				}
			}
			System.out.println();

			for (DeviceCohort dcobj : dclist) {
				try {
					System.out.println();
					String insertqry = "Insert into " + tableName
							+ " (`year`,`month`,`sales_channel`,`total_sales`,`same_month_registered`,"
							+ "`jan`,`feb`,`mar`,`apr`,`may`,`jun`,`jul`,`aug`,`sep`,`oct`,`nov`,`dec`,`un_identified_registered`,`un_identified_activated`,`period`,`month_no`,`total_act_rate`,`registered`,`same_month_activation`,`timezone`,`mtype_id`,`is_trial`,`is_bundle`)"
							+ "values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);";

					PreparedStatement stmt = db_connection_iris.prepareStatement(insertqry);
					stmt.setInt(1, dcobj.getYear());
					stmt.setString(2, dcobj.getMonth());
					stmt.setString(3, dcobj.getSales_ch());
					stmt.setInt(4, dcobj.getTotal_sales());
					stmt.setInt(5, dcobj.getSame_month_active());
					stmt.setInt(6, dcobj.getJanTotCnt());
					stmt.setInt(7, dcobj.getFebTotCnt());
					stmt.setInt(8, dcobj.getMarTotCnt());
					stmt.setInt(9, dcobj.getAprTotCnt());
					stmt.setInt(10, dcobj.getMayTotCnt());
					stmt.setInt(11, dcobj.getJunTotCnt());
					stmt.setInt(12, dcobj.getJulTotCnt());
					stmt.setInt(13, dcobj.getAugTotCnt());
					stmt.setInt(14, dcobj.getSepTotCnt());
					stmt.setInt(15, dcobj.getOctTotCnt());
					stmt.setInt(16, dcobj.getNovTotCnt());
					stmt.setInt(17, dcobj.getDecTotCnt());
					stmt.setInt(18, dcobj.getUnindentifiedregisteredcount());
					stmt.setInt(19, dcobj.getUnidentifiedactivatecount());
					stmt.setString(20, dcobj.getPeriod());
					stmt.setInt(21, dcobj.getMonth_no());
					stmt.setFloat(22, dcobj.getSame_month_act_rate());
					stmt.setInt(23, dcobj.getRegistered());
					stmt.setInt(24, dcobj.getSamemonthactivatedcount());
					stmt.setString(25, timezone);
					stmt.setLong(26, dcobj.getMtype_id());
					stmt.setInt(27, dcobj.getIs_trial());
					stmt.setInt(28, dcobj.getIs_bundle());
					log.info("Insert Query : " + stmt.toString());

					int i = stmt.executeUpdate();
					if (i > 0) {
						log.info("Month-" + dcobj.getMonth() + " Saleschannel-" + dcobj.getSales_ch() + " Inserted");
					} else {
						log.info(
								"Month-" + dcobj.getMonth() + " Saleschannel-" + dcobj.getSales_ch() + " not inserted");
					}
				} catch (Exception e) {
					String updateqry = "update " + tableName
							+ " set total_sales=?,same_month_registered=?,jan=?,feb=?,mar=?,apr=?,may=?,"
							+ "jun=?,jul=?,aug=?,sep=?,`oct`=?,nov=?,`dec`=?,un_identified_registered=?,un_identified_activated=?,total_act_rate=?,registered=?, same_month_activation=? where `year`=? and `month`=? and month_no=? and sales_channel=? and `period`=? and timezone=? and mtype_id=? and is_trial=? and is_bundle=?;";
					PreparedStatement stmt = db_connection_iris.prepareStatement(updateqry);

					stmt.setInt(1, dcobj.getTotal_sales());
					stmt.setInt(2, dcobj.getSame_month_active());
					stmt.setInt(3, dcobj.getJanTotCnt());
					stmt.setInt(4, dcobj.getFebTotCnt());
					stmt.setInt(5, dcobj.getMarTotCnt());
					stmt.setInt(6, dcobj.getAprTotCnt());
					stmt.setInt(7, dcobj.getMayTotCnt());
					stmt.setInt(8, dcobj.getJunTotCnt());
					stmt.setInt(9, dcobj.getJulTotCnt());
					stmt.setInt(10, dcobj.getAugTotCnt());
					stmt.setInt(11, dcobj.getSepTotCnt());
					stmt.setInt(12, dcobj.getOctTotCnt());
					stmt.setInt(13, dcobj.getNovTotCnt());
					stmt.setInt(14, dcobj.getDecTotCnt());
					stmt.setInt(15, dcobj.getUnindentifiedregisteredcount());
					stmt.setInt(16, dcobj.getUnidentifiedactivatecount());
					stmt.setFloat(17, dcobj.getSame_month_act_rate());
					stmt.setInt(18, dcobj.getRegistered());
					stmt.setInt(19, dcobj.getSamemonthactivatedcount());
					stmt.setInt(20, dcobj.getYear());
					stmt.setString(21, dcobj.getMonth());
					stmt.setInt(22, dcobj.getMonth_no());
					stmt.setString(23, dcobj.getSales_ch());
					stmt.setString(24, dcobj.getPeriod());
					stmt.setString(25, timezone);
					stmt.setLong(26, dcobj.getMtype_id());
					stmt.setInt(27, dcobj.getIs_trial());
					stmt.setInt(28, dcobj.getIs_bundle());

					log.info("\n Update Query : " + stmt.toString());
					int i = stmt.executeUpdate();
					if (i > 0) {
						log.info("Month-" + dcobj.getMonth() + " Saleschannel-" + dcobj.getSales_ch() + " updated");
					} else {
						log.info("Month-" + dcobj.getMonth() + " Saleschannel-" + dcobj.getSales_ch() + " not updated");
					}

				}
			}
		} catch (Exception e) {
			log.error("Error in saveDeviceCohort: " + e.getLocalizedMessage());
		} finally {
			try {
				db_connection_iris.close();
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return dclist;

	}

	private static int getMonthNo(String mn) {
		int monthno = 0;
		try {
			if (mn.equalsIgnoreCase("jan")) {
				monthno = 1;

			} else if (mn.equalsIgnoreCase("feb")) {
				monthno = 2;

			} else if (mn.equalsIgnoreCase("mar")) {
				monthno = 3;

			} else if (mn.equalsIgnoreCase("apr")) {
				monthno = 4;

			} else if (mn.equalsIgnoreCase("may")) {
				monthno = 5;

			} else if (mn.equalsIgnoreCase("jun")) {
				monthno = 6;

			} else if (mn.equalsIgnoreCase("jul")) {
				monthno = 7;

			} else if (mn.equalsIgnoreCase("aug")) {
				monthno = 8;

			} else if (mn.equalsIgnoreCase("sep")) {
				monthno = 9;

			} else if (mn.equalsIgnoreCase("oct")) {
				monthno = 10;

			} else if (mn.equalsIgnoreCase("nov")) {
				monthno = 11;

			} else if (mn.equalsIgnoreCase("dec")) {
				monthno = 12;

			}

		} catch (Exception e) {
			log.error("Error in getMonthNo" + e.getLocalizedMessage());
		}
		return monthno;

	}

	public static String getMinDate(int monthNumber, int year) {
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.YEAR, year);
		calendar.set(Calendar.MONTH, monthNumber - 1);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String strDate = dateFormat.format(calendar.getTime());
		return strDate;
	}
//
//	public static String getMaxDate(int monthNumber, int year) {
//		Calendar calendar = Calendar.getInstance();
//		calendar.set(Calendar.YEAR, year);
//		calendar.set(Calendar.MONTH, monthNumber - 1);
//		int lastDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
//		calendar.set(Calendar.DAY_OF_MONTH, lastDay);
//		calendar.set(Calendar.HOUR_OF_DAY, 23);
//		calendar.set(Calendar.MINUTE, 59);
//		calendar.set(Calendar.SECOND, 59);
//		calendar.set(Calendar.MILLISECOND, 999);
//		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//		String endDate = dateFormat.format(calendar.getTime());
//		return endDate;
//	}
	
	public static String getMaxDate(int monthNumber, int year) {
	    LocalDate lastDay = LocalDate.of(year, monthNumber, 1).with(TemporalAdjusters.lastDayOfMonth());
	    LocalDateTime endOfDay = lastDay.atTime(23, 59, 59);

	    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
	    return endOfDay.format(formatter);
	}

	private static void updateValueInTable(Map<String, String> appdwldCount, Connection db_connection_iris, int year) {
		log.info("Entered updateValueInTable");

		for (Entry<String, String> count : appdwldCount.entrySet()) {
			String downloadedDate = count.getKey();
			String dwldCount = count.getValue();
			String[] appCount = dwldCount.split(",");

			String monthname = getMonthName(downloadedDate);
			String[] month_name = monthname.split("-");
			monthname = month_name[0];
			int month_no = Integer.parseInt(month_name[1]);

			try {

				String insertQuery = "insert into app_downloads_count(datetime,ioscount,androidcount,year,month_name,month_no) values(?,?,?,?,?,?);";
				PreparedStatement stmt = db_connection_iris.prepareStatement(insertQuery);
				stmt.setString(1, downloadedDate);
				stmt.setString(2, appCount[0]);
				stmt.setString(3, appCount[1]);
				stmt.setInt(4, year);
				stmt.setString(5, monthname);
				stmt.setInt(6, month_no);
				int i = stmt.executeUpdate();
				if (i > 0) {
					log.info("Count value inserted for date " + downloadedDate);
				} else {
					log.info("Count value not inserted for date " + downloadedDate);
				}
			} catch (Exception e) {
				try {
					String update_qry = "update app_downloads_count set ioscount=?, androidcount=? where year=? and datetime=?";
					PreparedStatement stmt = db_connection_iris.prepareStatement(update_qry);
					stmt.setString(1, appCount[0]);
					stmt.setString(2, appCount[1]);
					stmt.setInt(3, year);
					stmt.setString(4, downloadedDate);
					System.out.println(stmt.toString());
					int i = stmt.executeUpdate();
					if (i > 0) {
						log.info("Count updated for date " + downloadedDate);
					} else {
						log.info("Count not updated for date " + downloadedDate);
					}
				} catch (Exception e1) {
					log.error("Exception while executing updateValueInTable : " + e1.getLocalizedMessage());
				}

			}
		}
		try {
			db_connection_iris.close();
		} catch (SQLException e) {
			log.error("Exception while executing updateValueInTable Connection error : " + e.getLocalizedMessage());
		}
	}

	public static String getCurrentYear(String timezone) {
		log.info("Entered getCurrentYear");
		String year = "1753";
		try {
			if (timezone.toLowerCase().equalsIgnoreCase("utc")) {
				String date = currentTime();
				SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
				Date formattedDate = dateFormat.parse(date);
				year = new SimpleDateFormat("yyyy").format(formattedDate);
				return year;

			} else if (timezone.toLowerCase().equalsIgnoreCase("pst")) {
				TimeZone pacificTimeZone = TimeZone.getTimeZone("America/Los_Angeles");
				long currentTime = new Date().getTime();
				long convertedTime = currentTime + pacificTimeZone.getOffset(currentTime);
				Calendar cal = Calendar.getInstance();
				cal.setTimeZone(TimeZone.getTimeZone("America/Los_Angeles"));
				cal.setTimeInMillis(convertedTime);
				year = String.valueOf(cal.get(Calendar.YEAR));
				return year;
			}

		} catch (Exception e) {
			log.error("Error in getCurrentYear " + e.getLocalizedMessage());
		}
		return null;
	}

	private static String getMonthName(String date) {
		int monthno = 0;
		String mname = "NA";
		log.info("Entered into getMonthName");
		try {
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
			Date formattedDate = dateFormat.parse(date);
			monthno = Integer.parseInt(new SimpleDateFormat("MM").format(formattedDate));
			String[] monthNames = { "January-01", "February-02", "March-03", "April-04", "May-05", "June-06", "July-07",
					"August-08", "September-09", "October-10", "November-11", "December-12" };
			mname = monthNames[monthno - 1];

		} catch (Exception e) {
			log.error("Exception in getMonthName : " + e.getLocalizedMessage());
		}

		return mname;
	}

	public static String currentTime() {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Calendar cal = Calendar.getInstance();
		Date curDate = cal.getTime();
		String currentTime = sdf.format(curDate);
		return currentTime;
	}

	public static String getDate(int day) {
		String strtdate = getDateFormat(day);
		return strtdate;
	}

	private static String getDateFormat(int day) {

		Calendar currDateCal = Calendar.getInstance();
		currDateCal.add(Calendar.DATE, day);

		Date dates = new Date(currDateCal.getTimeInMillis());
		// DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SS'Z'");
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

		String strtDate = dateFormat.format(dates);
		return strtDate;
	}

	// Update is_test = 1 for test users.
	private static void updateTestUser(Properties prop, boolean b) {
		log.info("Entered updateIrisTestUser");

		try {
			Connection db_connection_niom = null;
			con_niom = new Connection_DB(prop, "niom");
			db_connection_niom = con_niom.getConnection();

			// Update test user in user table
			String updateQuery = "UPDATE order_details od JOIN orders o ON o.order_id = od.order_id SET od.is_test=1 WHERE o.is_test=1;";

			int updateStatus = db_connection_niom.prepareStatement(updateQuery).executeUpdate();

			if (updateStatus > 0)
				log.info("Order_details for shopify Query updated for " + updateStatus + " rows.");
			else
				log.info("order_details for shopify Query is not updated!");

			// Update test user in invoice table
			updateQuery = "UPDATE order_details od JOIN orders o ON od.order_id = o.external_order_id SET od.is_test=1 WHERE o.is_test=1;";

			updateStatus = db_connection_niom.prepareStatement(updateQuery).executeUpdate();

			if (updateStatus > 0)
				log.info("Order_details for walmart Query updated for " + updateStatus + " rows.");
			else
				log.info("order_details for walmart Query is not updated!");

			try {
				if (db_connection_niom != null) {
					db_connection_niom.close();
					log.info("updateTestUser connections closed");
				}
			} catch (SQLException e) {
				log.error("Excep updateTestUser connections closed : " + e.getLocalizedMessage());
			}

		} catch (Exception e) {
			log.error("Exception in updateTestUser : " + e.getLocalizedMessage());
		}

	}
}