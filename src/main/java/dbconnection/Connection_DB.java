package dbconnection;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Properties;

public class Connection_DB {
	
	private static Connection conn;  
	private static String url = "***************************/";  
	private static String user = "nimble";//Username of database  
	private static String pass = "flamingo2010";//Password of database  


	public Connection_DB(Properties prop, String db)  {

		if (db.equals("iris")) {
			url = prop.getProperty("iris_url").trim();
			user = prop.getProperty("iris_username").trim();
			pass = prop.getProperty("iris_password").trim();
		} else if (db.equals("niom")) {
			url = prop.getProperty("niom_url").trim();
			user = prop.getProperty("niom_username").trim();
			pass = prop.getProperty("niom_password").trim();
		}else if (db.equals("datajar")) {
			url = prop.getProperty("urlDataJar").trim();
			user = prop.getProperty("dataJarUsername").trim();
			pass = prop.getProperty("dataJarPassword").trim();
		} else if (db.equals("iris_read")) {
			url = prop.getProperty("read_iris_url").trim();
			user = prop.getProperty("read_iris_username").trim();
			pass = prop.getProperty("read_iris_password").trim();
		}
	}


	public static Connection connect() throws SQLException
	{
		try{  
			Class.forName("com.mysql.cj.jdbc.Driver").newInstance();  
		}catch(ClassNotFoundException cnfe)
		{  
			System.err.println("Error: "+cnfe.getMessage());  
		}catch(InstantiationException ie)
		{  
			System.err.println("Error: "+ie.getMessage());  
		}catch(IllegalAccessException iae)
		{	  
			System.err.println("Error: "+iae.getMessage());  
		}  

		conn = (Connection) DriverManager.getConnection(url,user,pass);  
		return conn;
	}


	public static void setter(Properties prop) {
		
	}


	public static Connection getConnection() throws SQLException, ClassNotFoundException	
	{  
		if(conn != null && !conn.isClosed())  
			return conn;  
		connect();  
		return conn;  
	}

}
